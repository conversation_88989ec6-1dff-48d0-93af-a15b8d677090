/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.guia;

import Arquivo.ArquivoLog;
import Controller.Guias.GuiasSatWeb;
import Controller.Login.LoginSatMobWeb;
import Controller.Rotas.RotasSatWeb;
import Dados.Persistencia;
import Decoder.BASE64Encoder;
import SasBeans.Clientes;
import SasBeans.CxFGuiasVol;
import SasBeans.ExtratoFaturamento;
import SasBeans.Filiais;
import SasBeans.OS_Vig;
import SasBeans.Pedido;
import SasBeans.PedidoDN;
import SasBeans.PreOrder;
import SasBeans.PreOrderVol;
import SasBeans.Rt_Guias;
import SasBeans.Rt_Perc;
import SasBeans.SasPWFill;
import SasBeans.TesSaidasDN;
import SasBeansCompostas.BBPedidoAgencia;
import SasBeansCompostas.BBPedidoMalote;
import SasBeansCompostas.EGtv;
import SasBeansCompostas.GuiasPortal;
import SasBeansCompostas.ImportacaoPedido;
import SasBeansCompostas.PreOrderManifesto;
import SasDaos.EGtvDao;
import SasDaos.TesSaidasDNDao;
import br.com.sasw.lazydatamodels.EGtvLazyList;
import br.com.sasw.lazydatamodels.PedidosPreOrderLazyList;
import br.com.sasw.lazydatamodels.operacoes.PedidosLazyList;
import br.com.sasw.managedBeans.faturamento.ExtratoFaturamentoLazyList;

import br.com.sasw.pacotesuteis.controller.acessos.AcessosSatMobWeb;
import br.com.sasw.pacotesuteis.sasbeans.Rt_GuiasFat;
import br.com.sasw.pacotesuteis.sasbeans.TesSaidasDD;
import br.com.sasw.pacotesuteis.sasbeans.TesSaidasMD;
import br.com.sasw.pacotesuteis.sasdaos.TesSaidasDDDao;
import br.com.sasw.pacotesuteis.sasdaos.TesSaidasMDDao;
import SasBeansCompostas.NFiscalCliente;
import SasBeansCompostas.BoletoCliente;
import SasDaos.FatTVGuiasDao;
import SasDaos.NFiscalDao;
import br.com.sasw.lazydatamodels.NFiscalClienteLazyList;
import br.com.sasw.lazydatamodels.ExtratoFaturamentoClienteLazyList;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.PreencheEsquerda;
import br.com.sasw.pacotesuteis.utilidades.LerArquivo;
import br.com.sasw.pacotesuteis.utilidades.Logos;
import br.com.sasw.utils.Cores;
import br.com.sasw.utils.Mascaras;
import static br.com.sasw.utils.Mascaras.CEP;
import static br.com.sasw.utils.Mascaras.CNPJ;
import static br.com.sasw.utils.Mascaras.Data;
import static br.com.sasw.utils.Mascaras.Fone;
import static br.com.sasw.utils.Mascaras.Hora;
import static br.com.sasw.utils.Mascaras.Moeda;
import br.com.sasw.utils.Messages;
import static br.com.sasw.utils.Messages.getMessageS;
import static br.com.sasw.utils.Messages.getValorExtensoS;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.event.AjaxBehaviorEvent;
import javax.faces.view.ViewScoped;
import javax.imageio.ImageIO;
import javax.inject.Named;
import org.krysalis.barcode4j.impl.code128.Code128Bean;
import org.krysalis.barcode4j.output.bitmap.BitmapCanvasProvider;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.component.selectoneradio.SelectOneRadio;
import org.primefaces.component.tabview.TabView;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.TabChangeEvent;
import org.primefaces.event.ToggleEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.Visibility;
import org.primefaces.model.charts.ChartData;
import org.primefaces.model.charts.optionconfig.legend.Legend;
import org.primefaces.model.charts.optionconfig.legend.LegendLabel;
import org.primefaces.model.charts.optionconfig.title.Title;
import org.primefaces.model.charts.pie.PieChartDataSet;
import org.primefaces.model.charts.pie.PieChartModel;
import org.primefaces.model.charts.pie.PieChartOptions;
import org.w3c.dom.Document;
import org.w3c.tidy.Tidy;
import org.xhtmlrenderer.pdf.ITextRenderer;

/**
 * <AUTHOR>
 */
@Named(value = "guias")
@ViewScoped
public class GuiasClienteMB implements Serializable {

    private String codFil, data, datad, tipoPedido, banco, nomeFilial, data1, data2, codGrupo,
            nomecli, codcli, senhaAtual, senhaNova, operador, caminho, log, codBarras, extenso, autenticacao, voltar,
            mensagemImportacao, origemteste, hora1o, hora2o, hora1d, hora2d, agencia, subagencia,
            html, relatorio, tabela, coluna, linha, span, nomeArquivo, novoLacre, sequencia;
    private ArquivoLog logerro;
    private List<PreOrderManifesto> manifesto;
    private BigDecimal codPessoa, valorGuias, valor, volumesGuias;
    private Persistencia persistencia, central;
    private final GuiasSatWeb guiasweb;
    private final AcessosSatMobWeb acessosweb;
    private final LoginSatMobWeb login;
    private Rt_Guias guia;
    private EGtv guiaSelecionada, guiaPesquisa;
    private List<CxFGuiasVol> lacres;
    private List<OS_Vig> listaOsVig;
    private Calendar calendar;
    private Date dataSelecionada1, dataSelecionada2;
    private List<Date> datasSelecionadas;
    private LocalDate dataHoje;
    private OS_Vig os_vig, os_vigSelecionado;
    private Clientes origem, destino, faturamento;
    private Map filters, filtersPedido, filterPedidoPedido, filterExtrato;
    private Pedido pedido;
    private int total, volumesPreOrder;
    private LazyDataModel<EGtv> guias = null;
    private LazyDataModel<PreOrder> preOrders = null;
    private LazyDataModel<Pedido> pedidos = null;
    private LazyDataModel<ExtratoFaturamento> extratos = null;
    private LazyDataModel<NFiscalCliente> notasFiscais = null;
    private NFiscalCliente nfiscalSelecionada;
    private List<BoletoCliente> boletosNF;
    private final NFiscalDao nfiscalDao;
    private String filtroNumeroNF;
    private String filtroPracaNF;
    private List<File> arquivosPedidos;
    private List<Integer> lotes;
    private List<PreOrder> pedidosRecentes, preOrderDetalhado;
    private List<ImportacaoPedido> pedidosImportados;
    private PreOrder preOrderSelecionado, preOrderSelecionadoDetalhado;
    private List<BBPedidoAgencia> listaAgencias;
    private boolean limpar, editarComposicao;
    private SasPWFill filial;
    private StreamedContent arquivoDownload;
    private Filiais filiais;
    private List<Rt_Perc> aliviosNaData, reforcosNaData;
    private PieChartModel aliviosNaDataGrafico, reforcosNaDataGrafico;
    private final RotasSatWeb rotassatweb;
    private PedidoDN pedidoCedulaMoeda, pedidoCedulaMoedaNovo;
    private List<PedidoDN> listaPedidoCedulaMoeda;
    private final TesSaidasDNDao tesSaidasDNDao;
    private final TesSaidasDDDao tesSaidasDDDao;
    private final TesSaidasMDDao tesSaidasMDDao;

    public GuiasClienteMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codcli = (String) fc.getExternalContext().getSessionMap().get("cliente");
        nomecli = (String) fc.getExternalContext().getSessionMap().get("nomeCliente");
        agencia = (String) fc.getExternalContext().getSessionMap().get("agencia");
        subagencia = (String) fc.getExternalContext().getSessionMap().get("subagencia");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        voltar = (String) fc.getExternalContext().getSessionMap().get("origem");
        codGrupo = (String) fc.getExternalContext().getSessionMap().get("codgrupo");
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");

        calendar = Calendar.getInstance();
        calendar.setTime(Date.from(Instant.now()));
        datasSelecionadas = new ArrayList<>();
        datasSelecionadas.add(calendar.getTime()); // data inicial
        datasSelecionadas.add(calendar.getTime()); // data final

        data2 = calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        datasSelecionadas.get(0).setTime(calendar.getTimeInMillis()); // alterando data inicial
        data1 = calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        guiasweb = new GuiasSatWeb();
        acessosweb = new AcessosSatMobWeb();
        valorGuias = BigDecimal.ZERO;
        volumesGuias = BigDecimal.ZERO;
        os_vig = new OS_Vig();
        listaOsVig = new ArrayList<>();
        listaPedidoCedulaMoeda = new ArrayList<>();
        listaOsVig.add(os_vig);
        datad = new String();
        listaAgencias = new ArrayList<>();
        log = new String();
        pedido = new Pedido();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        login = new LoginSatMobWeb();
        logerro = new ArquivoLog(this.getClass().getSimpleName());
        tipoPedido = "recolhimento";
        data = DataAtual.getDataAtual("SQL");
        arquivosPedidos = new ArrayList<>();
        rotassatweb = new RotasSatWeb();
        tesSaidasDNDao = new TesSaidasDNDao();
        tesSaidasDDDao = new TesSaidasDDDao();
        tesSaidasMDDao = new TesSaidasMDDao();
        nfiscalDao = new NFiscalDao();
        boletosNF = new ArrayList<>();
        notasFiscais = null;
    }

    public void Persistencias(Persistencia pp, Persistencia central) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            this.central = central;
            if (null == this.central) {
                throw new Exception("ImpossivelConectarSatellite");
            }

            this.filters = new HashMap();
            this.filters.put("DtInicio", this.data1);
            this.filters.put("DtFim", this.data2);
            this.filters.put("CodCli", this.codcli);
            this.filters.put("CodPessoa", this.codPessoa.toBigInteger().toString());
            this.filters.put("Guia", "");
            this.filters.put("Lacre", "");
            this.filters.put(" CodFil = ? ", this.codFil.equals("0") ? "" : this.codFil);
            this.filters.put("CodFil = ? ", this.codFil.equals("0") ? "" : this.codFil);

            this.filtersPedido = new HashMap();
            this.filtersPedido.put(" PreOrder.DtColeta >= ? ", this.data1);
            this.filtersPedido.put(" PreOrder.DtColeta <= ? ", this.data2);
            this.filtersPedido.put("CodCli", this.codcli);
            this.filtersPedido.put("CodPessoa", this.codPessoa.toBigInteger().toString());
            this.filtersPedido.put(" PreOrder.Guia = ? ", "");
            this.filiais = this.rotassatweb.buscaInfoFilial(this.codFil, this.persistencia);

            this.filterPedidoPedido = new HashMap();
            this.filterPedidoPedido.put(" pedido.codfil = ? ", this.codFil.equals("0") ? "" : this.codFil);
            this.filterPedidoPedido.put(" pedido.data >= ? ", this.data1);
            this.filterPedidoPedido.put(" pedido.data <= ? ", this.data2);
            this.filterPedidoPedido.put(" pedido.flag_excl <> ? ", "*");
            this.filterPedidoPedido.put("CodCli", this.codcli);
            this.filterPedidoPedido.put("CodPessoa", this.codPessoa.toBigInteger().toString());

            this.filterExtrato = new HashMap();
            this.filterExtrato.put(" FatTvFecha.codfil = ? ", this.codFil);
            this.filterExtrato.put(" FatTvPlan.data >= ? ", this.data1);
            this.filterExtrato.put(" FatTvPlan.data <= ? ", this.data2);
            this.filterExtrato.put(" PessoaCliAut.flag_excl <> ? ", "*");
            this.filterExtrato.put(" FatTvFecha.NFGer = ? ", "S");
            this.filterExtrato.put(" PessoaCLiAut.Codigo = ? ", this.codPessoa.toPlainString());

            if (null != this.codcli && !this.codcli.equals("")) {
                this.pedidosRecentes = this.guiasweb.pedidosRecentes(this.codcli, this.codFil, this.persistencia);
            }

            gerarLog("Contando quantidade de guias.");
            this.total = this.guiasweb.totalGuias(this.filters, this.persistencia);
            gerarLog("Contando valor total das guias.");
            
            this.valorGuias = this.guiasweb.valorGuias(this.filters, this.persistencia);
            gerarLog("Contando quantidade total de volumes.");
            this.volumesGuias = this.guiasweb.qtdVolumes(this.filters, this.persistencia);

            this.guiaPesquisa = new EGtv();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../index.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    /**
     * Seleciona o trajeto com duplo clique
     *
     * @param event
     */
    public void tipoServico(AjaxBehaviorEvent event) {
        this.tipoPedido = (String) ((SelectOneRadio) event.getSource()).getValue();
    }

    public void selecionarDest(SelectEvent event) {
        this.os_vig = ((OS_Vig) event.getObject());
    }

    public void limparOrigemDestinoPortalPedido() {
        this.listaOsVig = new ArrayList<>();
        this.listaOsVig.add(this.os_vig);
    }

    public List<OS_Vig> buscarPedidos(String query) {
        try {
            boolean t;
            FacesContext fc = FacesContext.getCurrentInstance();

            if ((Boolean) fc.getExternalContext().getSessionMap().get("transpCacamba")) {
                if (this.tipoPedido.equals("recolhimento")) {
                    t = Boolean.TRUE;
                } else {
                    t = Boolean.FALSE;
                }
            } else {
                t = Boolean.TRUE;
            }

            this.listaOsVig = this.guiasweb.buscarPedidos(query, t, this.codPessoa.toString(), this.persistencia);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.listaOsVig;
    }

    public void preCadastro() {
        this.listaPedidoCedulaMoeda = new ArrayList<>();

        this.hora2o = new String();
        limparOrigemDestinoPortalPedido();
        this.hora2d = new String();
        this.pedido = new Pedido();
        this.valor = new BigDecimal(BigInteger.ZERO);
        this.datad = new String();
        this.hora1o = new String();
        this.hora1d = new String();
        this.tipoPedido = "recolhimento";

        PrimeFaces.current().resetInputs("pedido");
    }

    public void cadastrarPedido() {
        try {
            SimpleDateFormat formatoHora1 = new SimpleDateFormat("HH:mm");
            Calendar calendarioOrigem = Calendar.getInstance();
            Calendar calendarioDestino = Calendar.getInstance();

            calendarioOrigem.setTime(formatoHora1.parse(this.hora1o));
            calendarioOrigem.add(Calendar.MINUTE, +60);
            this.hora2o = formatoHora1.format(calendarioOrigem.getTime());
            calendarioDestino.setTime(formatoHora1.parse(this.hora1d));
            calendarioDestino.add(Calendar.MINUTE, +60);
            this.hora2d = formatoHora1.format(calendarioDestino.getTime());

            this.pedido.setCodFil(this.codFil);
            this.pedido.setSolicitante(this.pedido.getSolicitante().toUpperCase());
            this.pedido.setSituacao("PD");
            this.pedido.setTipo("T");
            if (this.tipoPedido.equals("recolhimento")) {
                this.pedido.setNRed1(this.os_vig.getNRed());
                this.pedido.setNRed2(this.os_vig.getNRedDst());
            } else {
                this.pedido.setNRed1(this.os_vig.getNRedDst());
                this.pedido.setNRed2(this.os_vig.getNRed());
            }
            this.os_vig = this.listaOsVig.get(0);
            this.pedido.setOS(this.os_vig.getOS().toString());
            this.pedido.setValor(this.valor);
            this.pedido.setData(this.datad);
            this.pedido.setDt_Incl(this.data);
            this.pedido.setHr_Incl(DataAtual.getDataAtual("HORA"));
            this.pedido.setDt_Alter(this.data);
            this.pedido.setHr_Alter(DataAtual.getDataAtual("HORA"));
            this.pedido.setHora1O(this.hora1o);
            this.pedido.setHora1D(this.hora1d);
            this.pedido.setOperIncl(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.pedido.setOperador(FuncoesString.RecortaAteEspaço(this.operador, 0, 10));
            this.pedido.setHora2O(this.hora2o);
            this.pedido.setHora2D(this.hora2d);
            this.pedido.setCodCli1(this.os_vig.getCliente());
            this.pedido.setCodCli2(this.os_vig.getCliDst());
            this.pedido.setFlag_Excl("");
            this.pedido.setPedidoCliente(this.pedido.getPedidoCliente().toUpperCase());
            this.pedido.setObs(this.pedido.getObs());
            if (this.pedido.getDt_Incl().equals(this.pedido.getData())) {
                this.pedido.setClassifSrv("E");
            } else {
                this.pedido.setClassifSrv("V");
            }

            if (null != this.listaPedidoCedulaMoeda && this.listaPedidoCedulaMoeda.size() > 0) {
                this.pedido.setListaPedidosComposicao(this.listaPedidoCedulaMoeda);
            }
            this.guiasweb.inserirPedido(this.pedido, this.persistencia);
            this.os_vig = new OS_Vig();
            this.listaOsVig = new ArrayList<>();
            this.listaOsVig.add(this.os_vig);
            getPedidos();
            PrimeFaces.current().executeScript("PF('dlgPedido').hide()");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("CadastroSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void exibirDetalhesPreOrder(ToggleEvent event) {
        this.preOrderSelecionado = (PreOrder) event.getData();
        if (event.getVisibility() != Visibility.HIDDEN) {
            try {
                this.preOrderDetalhado = this.guiasweb.detalhesPreOrder(this.preOrderSelecionado.getPedidoCliente(), this.persistencia);
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                this.log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(this.log, this.caminho);
            }
        }
    }

    public LazyDataModel<Pedido> getPedidos() {
        try {
            if (this.pedidos == null) {
                this.filterPedidoPedido.replace(" pedido.codfil = ? ", this.codFil);
                this.filterPedidoPedido.replace(" pedido.data >= ? ", this.data1);
                this.filterPedidoPedido.replace(" pedido.data <= ? ", this.data2);
                this.filterPedidoPedido.replace(" pedido.flag_excl <> ? ", "*");
                this.pedidos = new PedidosLazyList(this.persistencia, this.filterPedidoPedido, new ArrayList<>());
            } else {
                ((PedidosLazyList) this.pedidos).setFilters(this.filterPedidoPedido);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = "OPERADOR: " + this.operador + "\r\n" + e.getMessage();
            this.logerro.Grava(this.log, this.caminho);
        }
        return this.pedidos;
    }

    // Lista simples para teste (sem lazy loading)
    private List<ExtratoFaturamento> extratoSimples;

    // Novo extrato com filtros
    private List<ExtratoFaturamento> extratoCompleto;
    private List<ExtratoFaturamento> extratosFiltrados;

    // Filtros do novo extrato
    private String filtroGuia;
    private String filtroSerie;
    private boolean mostrarFiltrosExtrato = false;

    // Filtros das notas fiscais
    private String filtroNumeroNF_NF;
    private String filtroTipoServicoNF;
    private boolean mostrarFiltrosNF = false;
    private List<NFiscalCliente> notasFiscaisFiltradas;

    public List<ExtratoFaturamento> getExtratoSimples() {
        try {
            System.out.println("=== getExtratoSimples() CHAMADO ===");
            System.out.println("extratoSimples é null? " + (this.extratoSimples == null));

            if (this.extratoSimples == null) {
                System.out.println("Verificando dados necessários...");
                System.out.println("data1: " + this.data1);
                System.out.println("data2: " + this.data2);
                System.out.println("codPessoa: " + this.codPessoa);
                System.out.println("codFil: " + this.codFil);
                System.out.println("persistencia: " + (this.persistencia != null));
                System.out.println("central: " + (this.central != null));

                // Verificar se os dados necessários estão disponíveis
                if (this.data1 == null || this.data2 == null || this.codPessoa == null || this.codFil == null) {
                    System.out.println("ERRO: Dados insuficientes para carregar extrato");
                    return new ArrayList<>();
                }

                System.out.println("Carregando extrato simples...");
                FatTVGuiasDao dao = new FatTVGuiasDao();
                this.extratoSimples = dao.listarExtratoCliente(
                    this.codPessoa.toString(),
                    this.codFil,
                    this.data1,
                    this.data2,
                    null,
                    null,
                    this.persistencia,
                    this.central
                );
                System.out.println("Extrato simples carregado: " + (this.extratoSimples != null ? this.extratoSimples.size() : "null") + " registros");
            } else {
                System.out.println("Retornando extrato simples existente: " + this.extratoSimples.size() + " registros");
            }
            return this.extratoSimples;
        } catch (Exception e) {
            System.out.println("ERRO ao carregar extrato simples: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * Carrega o extrato completo (novo método)
     */
    public List<ExtratoFaturamento> getExtratosFiltrados() {
        try {
            System.out.println("=== getExtratosFiltrados() CHAMADO ===");

            if (this.extratoCompleto == null) {
                System.out.println("Carregando extrato completo...");

                // Verificar se os dados necessários estão disponíveis
                if (this.data1 == null || this.data2 == null || this.codPessoa == null || this.codFil == null) {
                    System.out.println("Dados insuficientes para carregar extrato");
                    return new ArrayList<>();
                }

                FatTVGuiasDao dao = new FatTVGuiasDao();
                this.extratoCompleto = dao.listarExtratoCliente(
                    this.codPessoa.toString(),
                    this.codFil,
                    this.data1,
                    this.data2,
                    null, // numeroNF
                    null, // pracaNF
                    this.persistencia,
                    this.central
                );
                System.out.println("Extrato completo carregado: " + (this.extratoCompleto != null ? this.extratoCompleto.size() : "null") + " registros");
            }

            // Aplicar filtros
            this.extratosFiltrados = aplicarFiltrosExtrato(this.extratoCompleto);
            System.out.println("Extrato filtrado: " + (this.extratosFiltrados != null ? this.extratosFiltrados.size() : "null") + " registros");

            return this.extratosFiltrados;
        } catch (Exception e) {
            System.out.println("ERRO ao carregar extrato filtrado: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * Aplica filtros no extrato
     */
    private List<ExtratoFaturamento> aplicarFiltrosExtrato(List<ExtratoFaturamento> lista) {
        if (lista == null || lista.isEmpty()) {
            return new ArrayList<>();
        }

        List<ExtratoFaturamento> resultado = new ArrayList<>(lista);

        // Filtro por NF
        if (this.filtroNumeroNF != null && !this.filtroNumeroNF.trim().isEmpty()) {
            resultado = resultado.stream()
                .filter(e -> e.getNF() != null && e.getNF().contains(this.filtroNumeroNF.trim()))
                .collect(java.util.stream.Collectors.toList());
        }

        // Filtro por Praça
        if (this.filtroPracaNF != null && !this.filtroPracaNF.trim().isEmpty()) {
            resultado = resultado.stream()
                .filter(e -> e.getPraca() != null && e.getPraca().contains(this.filtroPracaNF.trim()))
                .collect(java.util.stream.Collectors.toList());
        }

        // Filtro por Guia
        if (this.filtroGuia != null && !this.filtroGuia.trim().isEmpty()) {
            resultado = resultado.stream()
                .filter(e -> e.getGuia() != null && e.getGuia().contains(this.filtroGuia.trim()))
                .collect(java.util.stream.Collectors.toList());
        }

        // Filtro por Série
        if (this.filtroSerie != null && !this.filtroSerie.trim().isEmpty()) {
            resultado = resultado.stream()
                .filter(e -> e.getSerie() != null && e.getSerie().contains(this.filtroSerie.trim()))
                .collect(java.util.stream.Collectors.toList());
        }

        return resultado;
    }

    public LazyDataModel<ExtratoFaturamento> getExtrato() {
        try {
            System.out.println("=== getExtrato() CHAMADO ===");
            if (this.extratos == null) {
                // Verificar se os dados necessários estão disponíveis
                if (this.data1 == null || this.data2 == null || this.codPessoa == null || this.codFil == null) {
                    System.out.println("Dados insuficientes para carregar extrato");
                    System.out.println("data1: " + this.data1 + ", data2: " + this.data2 + ", codPessoa: " + this.codPessoa + ", codFil: " + this.codFil);
                    return null;
                }

                System.out.println("Criando ExtratoFaturamentoClienteLazyList");
                System.out.println("Parâmetros: codPessoa=" + this.codPessoa + ", codFil=" + this.codFil + ", data1=" + this.data1 + ", data2=" + this.data2);
                System.out.println("Persistencias: local=" + (this.persistencia != null) + ", central=" + (this.central != null));

                this.extratos = new ExtratoFaturamentoClienteLazyList(
                    this.persistencia,
                    this.central,
                    this.codPessoa,
                    this.codFil,
                    this.data1,
                    this.data2
                );
                System.out.println("ExtratoFaturamentoClienteLazyList criado com sucesso");
            } else {
                System.out.println("LazyDataModel do extrato já existe, retornando existente");
            }
            return this.extratos;
        } catch (Exception e) {
            System.out.println("Erro ao criar LazyDataModel para extrato: " + e.getMessage());
            e.printStackTrace();
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                "Erro ao carregar extrato: " + e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            return null;
        }
    }

    public LazyDataModel<PreOrder> getAllPedidos() {
        if (this.preOrders == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabelaPedido");
            this.filtersPedido.replace(" PreOrder.DtColeta >= ? ", this.data1);
            this.filtersPedido.replace(" PreOrder.DtColeta <= ? ", this.data2);
            this.filtersPedido.replace("CodCli", this.codcli);
            dt.setFilters(this.filtersPedido);
            this.preOrders = new PedidosPreOrderLazyList(this.persistencia);
        }
        return this.preOrders;
    }

    public LazyDataModel<EGtv> getAllGuias() {
        if (this.guias == null) {
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabela");
            this.filters.replace("DtInicio", this.data1);
            this.filters.replace("DtFim", this.data2);
            this.filters.replace("CodCli", this.codcli);
            this.filters.replace("CodPessoa", this.codPessoa.toBigInteger().toString());
            this.filters.replace("Guia", "");
            this.filters.replace("Lacre", "");
            this.filters.replace(" CodFil = ? ", this.codFil.equals("0") ? "" : this.codFil);
            this.filters.replace("CodFil = ? ", this.codFil.equals("0") ? "" : this.codFil);
            dt.setFilters(this.filters);
            this.guias = new EGtvLazyList(this.persistencia);

        }
        return this.guias;
    }

    public void consultarGuiasPortal() throws Exception {
        // Capturar códigos para exclusão
        String inSeqRota = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("seqRota"),
                inParada = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("parada");

        EGtvDao dao = new EGtvDao();

        List<GuiasPortal> Retorno = dao.editarGuiaLancada(inSeqRota, inParada, this.persistencia);

        Gson gson = new GsonBuilder().create();
        PrimeFaces.current().executeScript("PreAbrirFormAddGuias(" + gson.toJson(Retorno) + ", '" + inSeqRota + "', '" + inParada + "')");
    }

    public void selecionarDatas(SelectEvent event) {
        try {
            this.datasSelecionadas = (ArrayList) event.getObject();
            if (this.datasSelecionadas.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneDataFinal"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.data1 = this.datasSelecionadas.get(0).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                this.data2 = this.datasSelecionadas.get(1).toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

                this.dataSelecionada1 = this.datasSelecionadas.get(0);
                this.dataSelecionada2 = this.datasSelecionadas.get(1);

                this.guias = null;
                this.filters.replace("DtInicio", this.data1);
                this.filters.replace("DtFim", this.data2);
                //DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabela");
                //dt.setFilters(this.filters);
                gerarLog("Listando guias");
                //getAllGuias();
                //dt.setFirst(0);

                this.preOrders = null;

                // Recarregar notas fiscais quando a data muda
                this.recarregarNotasFiscais();

                // Recarregar extrato quando a data muda
                this.extratos = null;
                this.extratoSimples = null;
                this.extratoCompleto = null;
                this.extratosFiltrados = null;
                //DataTable dtPedido = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabelaPedido");
                this.filtersPedido.replace(" PreOrder.DtColeta >= ? ", this.data1);
                this.filtersPedido.replace(" PreOrder.DtColeta <= ? ", this.data2);
                //dtPedido.setFilters(this.filtersPedido);
                //getAllPedidos();

                this.pedidos = null;
                //dtPedido.setFirst(0);
                this.filterPedidoPedido.put(" pedido.data >= ? ", this.data1);
                this.filterPedidoPedido.put(" pedido.data <= ? ", this.data2);
                //getPedidos();

                this.extratos = null;
                this.extratoSimples = null;
                this.extratoCompleto = null;
                this.extratosFiltrados = null;
                DataTable dtExtrato = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabelaExtrato");
                this.filterExtrato.replace(" FatTvPlan.data >= ? ", this.data1);
                this.filterExtrato.replace(" FatTvPlan.data <= ? ", this.data2);
                //dtExtrato.setFilters(this.filterExtrato);
                //getExtrato();
                //dtExtrato.setFirst(0);

                gerarLog("Contando quantidade de guias.");
                this.total = this.guiasweb.totalGuias(this.filters, this.persistencia);
                gerarLog("Contando valor total das guias.");
                this.valorGuias = this.guiasweb.valorGuias(this.filters, this.persistencia);
                gerarLog("Contando quantidade de volumes de guias.");
                this.volumesGuias = this.guiasweb.qtdVolumes(this.filters, this.persistencia);

                PrimeFaces.current().ajax().update("main", "cabecalho", "totais");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void dataAnterior() {
        try {

            this.calendar.setTime(this.datasSelecionadas.get(0));
            this.calendar.add(Calendar.DAY_OF_YEAR, -1);
            this.datasSelecionadas.get(0).setTime(this.calendar.getTimeInMillis());
            this.data1 = this.calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.calendar.setTime(this.datasSelecionadas.get(1));
            this.calendar.add(Calendar.DAY_OF_YEAR, -1);
            this.datasSelecionadas.get(1).setTime(this.calendar.getTimeInMillis());
            this.data2 = this.calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.dataSelecionada1 = this.datasSelecionadas.get(0);
            this.dataSelecionada2 = this.datasSelecionadas.get(1);

            this.filters.replace("DtInicio", this.data1);
            this.filters.replace("DtFim", this.data2);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabela");
            dt.setFilters(this.filters);
            gerarLog("Listando guias");
            getAllGuias();
            dt.setFirst(0);

            DataTable dtPedido = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabelaPedido");
            this.filtersPedido.replace(" PreOrder.DtColeta >= ? ", this.data1);
            this.filtersPedido.replace(" PreOrder.DtColeta <= ? ", this.data2);
            dtPedido.setFilters(this.filtersPedido);
            getAllPedidos();

            // Recarregar notas fiscais quando a data muda
            this.recarregarNotasFiscais();

            // Recarregar extrato quando a data muda
            this.extratos = null;
            this.extratoSimples = null;
            this.extratoCompleto = null;
            this.extratosFiltrados = null;
            dtPedido.setFirst(0);

            this.filterPedidoPedido.put(" pedido.data >= ? ", this.data1);
            this.filterPedidoPedido.put(" pedido.data <= ? ", this.data2);
            getPedidos();

            DataTable dtExtrato = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabelaExtrato");
            this.filterExtrato.replace(" FatTvPlan.data >= ? ", this.data1);
            this.filterExtrato.replace(" FatTvPlan.data <= ? ", this.data2);
            dtExtrato.setFilters(this.filterExtrato);
            getExtrato();
            dtExtrato.setFirst(0);

            gerarLog("Contando quantidade de guias.");
            this.total = this.guiasweb.totalGuias(this.filters, this.persistencia);
            gerarLog("Contando valor total das guias.");
            this.valorGuias = this.guiasweb.valorGuias(this.filters, this.persistencia);
            gerarLog("Contando quantidade de volumcontagemVolumeses de guias.");
            this.volumesGuias = this.guiasweb.qtdVolumes(this.filters, this.persistencia);

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void dataPosterior() {
        try {
            this.calendar.setTime(this.datasSelecionadas.get(0));
            this.calendar.add(Calendar.DAY_OF_YEAR, 1);
            this.data1 = this.calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.calendar.setTime(this.datasSelecionadas.get(1));
            this.calendar.add(Calendar.DAY_OF_YEAR, 1);
            this.data2 = this.calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.dataSelecionada1 = this.datasSelecionadas.get(0);
            this.dataSelecionada2 = this.datasSelecionadas.get(1);

            this.filters.replace("DtInicio", this.data1);
            this.filters.replace("DtFim", this.data2);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabela");
            dt.setFilters(this.filters);
            gerarLog("Listando guias");
            getAllGuias();
            dt.setFirst(0);

            DataTable dtPedido = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabelaPedido");
            this.filtersPedido.replace(" PreOrder.DtColeta >= ? ", this.data1);
            this.filtersPedido.replace(" PreOrder.DtColeta <= ? ", this.data2);
            dtPedido.setFilters(this.filtersPedido);
            getAllPedidos();
            dtPedido.setFirst(0);

            // Recarregar notas fiscais quando a data muda
            this.recarregarNotasFiscais();

            // Recarregar extrato quando a data muda
            this.extratos = null;
            this.extratoSimples = null;
            this.extratoCompleto = null;
            this.extratosFiltrados = null;

            this.filterPedidoPedido.put(" pedido.data >= ? ", this.data1);
            this.filterPedidoPedido.put(" pedido.data <= ? ", this.data2);
            getPedidos();

            DataTable dtExtrato = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabelaExtrato");
            this.filterExtrato.replace(" FatTvPlan.data >= ? ", this.data1);
            this.filterExtrato.replace(" FatTvPlan.data <= ? ", this.data2);
            dtExtrato.setFilters(this.filterExtrato);
            getExtrato();
            dtExtrato.setFirst(0);

            gerarLog("Contando quantidade de guias.");
            this.total = this.guiasweb.totalGuias(this.filters, this.persistencia);
            gerarLog("Contando valor total das guias.");
            this.valorGuias = this.guiasweb.valorGuias(this.filters, this.persistencia);
            gerarLog("Contando quantidade de volumes de guias.");
            this.volumesGuias = this.guiasweb.qtdVolumes(this.filters, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void TrocarSenha() {
        try {
            Boolean senhaValida = this.login.VerificaSenha(this.codPessoa, this.senhaAtual, this.persistencia);
            if (senhaValida) {
                this.acessosweb.trocarSenhaCliente(this.codPessoa, this.senhaNova,
                        FuncoesString.RecortaAteEspaço(operador, 0, 10), this.persistencia, this.central);
            } else {
                throw new Exception("SenhaIncorreta");
            }
            PrimeFaces.current().executeScript("PF('dlgTrocarSenha').hide()");
            PrimeFaces.current().executeScript("PF('dlgOk').hide()");
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SenhaAlteradaSucesso"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void imprimirDblClick() {
    TabView mainTabView = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView");
        switch (mainTabView.getActiveIndex()) {
            case 0:
                if (null == this.guiaSelecionada) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS("GuiaIndisponivel"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                } 
                else{
                    imprimir();
                }
                break;
        }
    }
    
    public void imprimir() {
//        mainTabView
        TabView mainTabView = (TabView) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView");
        switch (mainTabView.getActiveIndex()) {
            case 0:
                if (null == this.guiaSelecionada) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneGuiaEmitida"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                } else {
                    try {
                        this.relatorio = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/relatorio.html"));
                        this.tabela = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/tabela.html"));
                        this.linha = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/linha.html"));
                        this.coluna = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/coluna.html"));
                        this.span = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/span.html"));

                        this.guia = this.guiasweb.obterInfoGuia(this.guiaSelecionada, this.persistencia);

                        if (this.guia.getGuia() == null || this.guia.getSerie() == null) {
                            throw new Exception("GuiaIndisponivel");
                        }

                        this.guia.setAssRemetente(this.guiaSelecionada.getAssinatura());
                        this.guia.setAssDestinatario(this.guiaSelecionada.getAssinaturaDestino()); 
                        // 5531000000201
                        this.guia.setCodBarras(PreencheEsquerda(this.guia.getGuia().toBigInteger().toString(), 10, "0") + " " + this.guia.getSerie());
                        
                        this.guia.setValorExtenso(Messages.getValorExtensoS(this.guia.getValor().toPlainString()));
                        this.guia.setAutenticacao(tratarAutenticacao(this.guia.getCnpjFilial(), this.guia.getGuia().toBigInteger().toString(),
                                this.guia.getSerie(), this.guia.getSequencia().toBigInteger().toString(), String.valueOf(this.guia.getParada())));
//                this.guia.setAutenticacao("");
                        this.guia.setLacres(this.guiasweb.listarLacres(this.guia.getGuia().toBigInteger().toString(), this.guia.getSerie(), this.persistencia));

                        this.nomeArquivo = this.guia.getGuia().toBigInteger() + ".pdf";

                        StringBuilder guiaImpressa = new StringBuilder();
                        StringBuilder guiaImpressaAuxTextoTabela, guiaImpressaAuxTextoLinha, guiaImpressaAuxTextoColuna;

                        guiaImpressaAuxTextoTabela = new StringBuilder();

                        Code128Bean bean = new Code128Bean();
                        bean.setHeight(10d);
                        bean.setModuleWidth(0.35);
                        bean.doQuietZone(false);
                        BitmapCanvasProvider provider = new BitmapCanvasProvider(110, BufferedImage.TYPE_BYTE_GRAY, false, 0);
                        bean.generateBarcode(provider, this.guia.getCodBarras());
                        provider.finish();
                        BufferedImage imagem = provider.getBufferedImage();
                        ByteArrayOutputStream os = new ByteArrayOutputStream();
                        ImageIO.write(imagem, "png", os);

                        String codigoBarras = new BASE64Encoder().encode(os.toByteArray());

                        FacesContext fc = FacesContext.getCurrentInstance();
                        String NomeRef = !(Boolean) fc.getExternalContext().getSessionMap().get("transpCacamba") ? this.persistencia.getEmpresa() : "CACAMBA";

                        switch (NomeRef) {
                            case "CACAMBA":
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "5").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", "<img src=\"" + Logos.getLogo(this.persistencia.getEmpresa(), this.guia.getCodFil().toPlainString())
                                                + "\" height=\"47px\" width=\"59px\"/>"));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", "ECO VISÃO")));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", this.guia.getRazaoSocial())));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.guia.getEnderecoFilial() + "," + this.guia.getBairroFilial()));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.guia.getCidadeFilial() + "/" + this.guia.getUfFilial() + "&nbsp;-&nbsp;"
                                                + CEP(this.guia.getCepFilial())));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("CGC"))
                                                + ": &nbsp;" + CNPJ(this.guia.getCnpjFilial())
                                                + this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Telefone"))
                                                + ": &nbsp;" + Fone(this.guia.getFoneFilial())));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; "
                                        + "text-align: left; width: 100%; background: #fff; overflow: hidden; width: 335px;  font-size: 16px; "
                                        + "padding: 3px 3px; color: #000000; font-size: 16px; font-weight: normal;")
                                        .replace("@IdTabela", "cabecalho").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                                //filialOS
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                if (this.guia.getEr().equals("R")) {
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                            .replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", "TERMO DE COLETA DE CONTAINER")));
                                } else {
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                            .replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", "TERMO DE ENTREGA DE CONTAINER")));
                                }
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "width: 100%;")
                                        .replace("@WidthTD", "100%").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Filial"))
                                                + ": &nbsp;" + PreencheEsquerda(this.guia.getCodFil().toString().replace(".0", ""), 4, "0")));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "2").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", "<img src=\"data:image/png;base64," + codigoBarras + "\" width=\"250\" height=\"60\" alt=\"embedded folder icon\">"));

                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("OS"))
                                                + ": &nbsp;" + this.guia.getOS().toString().replace(".0", "")));

                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left;"
                                        + " width: 100%; background: #fff; overflow: hidden; width: 335px;  font-size: 16px; padding: 3px 3px; "
                                        + "color: #000000; font-size: 16px; font-weight: normal;")
                                        .replace("@IdTabela", "filialOS").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                                //origem
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black;")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", getMessageS("DadosCliente"))));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                        .replace("@StyleTR", "border-top: 1px solid black; border-bottom: 1px solid black;")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                if (this.guia.getEr().equals("R")) {
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center;border-top: 1px solid black;")
                                            .replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", this.guia.getNomeOri())));
                                } else {
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center;border-top: 1px solid black;")
                                            .replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", this.guia.getNomeDst())));
                                }
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                if (this.guia.getEr().equals("R")) {
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center;")
                                            .replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Remetente") + ": ")
                                                    + " " + this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "")
                                                            .replace("@TextoSpan", this.guia.getnRedOri())));
                                } else {
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center;")
                                            .replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Remetente") + ": ")
                                                    + " " + this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "")
                                                            .replace("@TextoSpan", this.guia.getnRedDst())));
                                }
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                if (this.guia.getEr().equals("R")) {
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Registro") + ": ")
                                                    + " "
                                                    + (this.guia.getRegistroOri().length() == 14 ? Mascaras.CNPJ(this.guia.getRegistroOri())
                                                    : (this.guia.getRegistroOri().length() == 11 ? Mascaras.CPF(this.guia.getRegistroOri()) : this.guia.getRegistroOri()))));
                                } else {
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Registro") + ": ")
                                                    + " "
                                                    + (this.guia.getRegistroDst().length() == 14 ? Mascaras.CNPJ(this.guia.getRegistroDst())
                                                    : (this.guia.getRegistroDst().length() == 11 ? Mascaras.CPF(this.guia.getRegistroDst()) : this.guia.getRegistroDst()))));
                                }
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                if (this.guia.getEr().equals("R")) {
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Endereco") + ": ")
                                                    + " " + this.guia.getEndOri()));
                                } else {
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Endereco") + ": ")
                                                    + " " + this.guia.getEndDst()));
                                }
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 335px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                        .replace("@IdTabela", "origem").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                                //enderecoOrigem
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                if (this.guia.getEr().equals("R")) {
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.guia.getBairroOri() + ", " + this.guia.getCidadeOri() + "/" + this.guia.getEstadoOri()));
                                } else {
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.guia.getBairroDst() + ", " + this.guia.getCidadeDst() + "/" + this.guia.getEstadoDst()));
                                }
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 335px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                        .replace("@IdTabela", "enderecoOrigem").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                                //dadosOrigem
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                if (this.guia.getEr().equals("R")) {
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", getMessageS("Coleta") + ": ")
                                                    + "&nbsp;"
                                                    + Data(this.guia.getColetaOri())));
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").
                                            replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", getMessageS("Veiculo") + ": ")
                                                    + "&nbsp;"
                                                    + this.guia.getVeiculoOri().replace(".0", "")));
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", getMessageS("Rota") + ": ")
                                                    + "&nbsp;"
                                                    + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;")
                                                            .replace("@ClassSpan", "").replace("@TextoSpan", this.guia.getRotaOri().replace(".0", ""))));
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", getMessageS("Chegada") + ": ")
                                                    + Hora(this.guia.getHora1())));
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", getMessageS("Said") + ": ")
                                                    + Hora(this.guia.getHora2())));
                                } else {
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", getMessageS("Entrega") + ": ")
                                                    + "&nbsp;"
                                                    + Data(this.guia.getColetaDst())));
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").
                                            replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", getMessageS("Veiculo") + ": ")
                                                    + "&nbsp;"
                                                    + this.guia.getVeiculoDst().replace(".0", "")));
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", getMessageS("Rota") + ": ")
                                                    + "&nbsp;"
                                                    + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;")
                                                            .replace("@ClassSpan", "").replace("@TextoSpan", this.guia.getRotaDst().replace(".0", ""))));
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", getMessageS("Chegada") + ": ")
                                                    + Hora(this.guia.getHoraChegada())));
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", getMessageS("Said") + ": ")
                                                    + Hora(this.guia.getHoraSaida())));
                                }
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                        .replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 335px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                        .replace("@IdTabela", "dadosOrigem").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                                //detalhes
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black;")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", getMessageS("IdentificacaoContainer"))));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black;")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan",
                                                        (this.guia.getLacres().size() == 1 ? getMessageS("Container") : getMessageS("Containers"))
                                                        + ": ")));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                for (CxFGuiasVol lacre : this.guia.getLacres()) {
                                    guiaImpressaAuxTextoColuna = new StringBuilder();
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                            .replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", lacre.getLacre())));
                                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                                }

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "text-align: justify; padding-right: 4px !important;border-top: 1px solid black;")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", "Ao receber este documento, concordo com os seguintes termos:"
                                                        + "</br>"
                                                        + "</br> * É necessário que seja feita a reserva da vaga para colocação da caçamba no local desejado. O espaço a ser reservado equivale a vaga de um carro."
                                                        + "</br> * Não é permitido a colocação de caçambas em pontos de ônibus, táxi, Pne ou cobrindo bueiros. "
                                                        + "</br> * Não é permitido a mudança de local da caçamba por parte do contratante."
                                                        + "</br> * Não é permitido colocar resíduo hospitalar, químico, orgânico, industrial e lixo doméstico dentro da caçamba."
                                                        + "</br> * Não é permitido colocar fogo no conteúdo da caçamba. "
                                                        + "</br> * É proibido colocar entulho ultrapassando a borda da caçamba."
                                                        + "</br> * Fica o locatário responsável pela caçamba recebida, durante o período de permanência da mesma no local entregue."
                                                        + "</br> * Não ultrapasse o limite da borda do container. Evite ser multado.")));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                        .replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; border-right: 1px solid black ;vertical-align: baseline;")
                                        .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("AssRemetente") + ": ")));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; vertical-align: baseline;")
                                        .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("AssDestinatario") + ": ")));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-right: 1px solid black;vertical-align: baseline;")
                                        .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.guia.getAssRemetente().replace("Ch.Eq: ", "Motorista: ")));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "vertical-align: baseline;text-align: center;")
                                        .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                        .replace("@TextoTD",
                                                "<img src=\"https://mobile.sasw.com.br:9091/satellite/assinaturas/" + this.persistencia.getEmpresa()
                                                + "/" + this.guiaSelecionada.getSequencia().replace(".0", "") + "/" + this.guia.getParada()
                                                + ".png\" height=\"47px\" width=\"59px\" alt=\"" + this.guia.getAssDestinatario().replace("Ch.Eq: ", "Motorista: ") + "\"/>"));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                // Forma de pagamento
                                Rt_GuiasFat rt_GuiasFat = this.guiasweb.obterFormaPagamentoGuia(this.guiaSelecionada.getGuia(), this.guiaSelecionada.getSerie(), this.persistencia);
                                if (rt_GuiasFat != null) {
                                    guiaImpressaAuxTextoColuna = new StringBuilder();
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "border-top: 1px solid black;")
                                            .replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", getMessageS("Valor")
                                                            + ": ")));
                                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                    guiaImpressaAuxTextoColuna = new StringBuilder();
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                            .replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", Moeda(rt_GuiasFat.getValorTot()))));
                                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                    guiaImpressaAuxTextoColuna = new StringBuilder();
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "border-bottom: 1px solid black;")
                                            .replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", Messages.getValorExtensoS(rt_GuiasFat.getValorTot()))));
                                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                    guiaImpressaAuxTextoColuna = new StringBuilder();
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "border-top: 1px solid black;")
                                            .replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", getMessageS("FormaPagamento")
                                                            + ": ")));
                                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                    guiaImpressaAuxTextoColuna = new StringBuilder();
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "border-bottom: 1px solid black;")
                                            .replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", rt_GuiasFat.getFormaPgtoDescricao())));
                                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                                }

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; ").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", getMessageS("Autenticacao"))));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", tratarAutenticacao(this.guiaSelecionada.getCnpjFilial(),
                                                        this.guiaSelecionada.getGuia().replace(".0", ""),
                                                        this.guiaSelecionada.getSerie(),
                                                        this.guiaSelecionada.getSequencia().replace(".0", ""),
                                                        this.guiaSelecionada.getParada())) + "&nbsp;"));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; border-bottom: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Obs") + ": ")
                                                + "&nbsp;"
                                                + this.guia.getObserv()));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                        .replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; "
                                        + "width: 100%;background: #fff;overflow: hidden; width: 335px;border-right: 1px solid black;"
                                        + "border-left: 1px solid black;font-size: 14px; margin-bottom: 10px;")
                                        .replace("@IdTabela", "detalhes").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));
                                break;
                            default:
                                // GERAÇÃO GUIA ELETRÔNICA
                                //cabecalho
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "4").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", "<img src=\"" + Logos.getLogo(this.persistencia.getEmpresa(), this.codFil)
                                                + "\" height=\"47px\" width=\"59px\"/>"));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", this.guiaSelecionada.getRazaoSocial())));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.guiaSelecionada.getEnderecoFilial() + "," + this.guiaSelecionada.getBairroFilial()));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.guiaSelecionada.getCidadeFilial() + "/" + this.guiaSelecionada.getUfFilial() + "&nbsp;-&nbsp;"
                                                + CEP(this.guiaSelecionada.getCepFilial())));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("CGC"))
                                                + ": &nbsp;" + CNPJ(this.guiaSelecionada.getCnpjFilial())
                                                + this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Telefone"))
                                                + ": &nbsp;" + Fone(this.guiaSelecionada.getFoneFilial())));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 500px !important; background: #fff; overflow: hidden; width: 500px;  font-size: 16px; padding: 3px 3px; color: #000000; font-size: 16px; font-weight: normal;")
                                        .replace("@IdTabela", "cabecalho").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                                //filialOS
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                String textoTit = "";
                                if (null != this.guiaSelecionada.getHrCheg()
                                        && !this.guiaSelecionada.getHrCheg().equals("")
                                        && !this.guiaSelecionada.getHrCheg().equals(":")
                                        && !this.guiaSelecionada.getHrCheg().equals("  :  ")
                                        && !this.guiaSelecionada.getHrCheg().equals(" : ")) {
                                    textoTit = getMessageS("GETV");
                                } else {
                                    textoTit = getMessageS("RPVdescr");
                                }

                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", textoTit)));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "width: 100%;")
                                        .replace("@WidthTD", "100%").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Filial"))
                                                + ": &nbsp;" + PreencheEsquerda(this.guiaSelecionada.getCodFil().replace(".0", ""), 4, "0")));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "2").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", "<img src=\"data:image/png;base64," + codigoBarras + "\" width=\"250\" height=\"60\" alt=\"embedded folder icon\">"));

                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                String NumeroOsRef = "";

                                if (!this.guiaSelecionada.getOS().replace(".0", "").equals("0")) {
                                    NumeroOsRef = this.guiaSelecionada.getOS().replace(".0", "");
                                } else {
                                    NumeroOsRef = this.guia.getOS().toString().replace(".0", "");
                                }

                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("OS"))
                                                + ": &nbsp;" + NumeroOsRef));

                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%; background: #fff; overflow: hidden; width: 500px;  font-size: 16px; padding: 3px 3px; color: #000000; font-size: 16px; font-weight: normal;")
                                        .replace("@IdTabela", "filialOS").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                                //cliente
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black;")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("ClienteP") + ": ")));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                        .replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black;")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", this.guiaSelecionada.getnRedFat())));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "italic").replace("@TextoSpan", getMessageS("Endereco"))
                                                + ": &nbsp;" + this.guiaSelecionada.getEndFat()));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.guiaSelecionada.getBairroFat() + ", " + this.guiaSelecionada.getCidadeFat() + "/" + this.guiaSelecionada.getEstadoFat()));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                        .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("CGC"))
                                                + ": &nbsp;" + CNPJ(this.guiaSelecionada.getCgcFat())));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                        .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("InscEstadual"))
                                                + ": &nbsp;" + this.guiaSelecionada.getIeFat()));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                        .replace("@IdTabela", "cliente").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                                //origem
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black;")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", getMessageS("DadosOrigem"))));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                        .replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Remetente") + ": ")
                                                + " " + this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "")
                                                        .replace("@TextoSpan", this.guiaSelecionada.getAgSB() + " " + this.guiaSelecionada.getnRedOri())));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Endereco") + ": ")
                                                + " " + this.guiaSelecionada.getEndOri()));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                        .replace("@IdTabela", "origem").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                                //enderecoOrigem
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.guiaSelecionada.getBairroOri() + ", " + this.guiaSelecionada.getCidadeOri() + "/" + this.guiaSelecionada.getEstadoOri()));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                        .replace("@IdTabela", "enderecoOrigem").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                                //dadosOrigem
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Data") + ": ")
                                                + "&nbsp;"
                                                + Data(this.guiaSelecionada.getDtColeta())));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").
                                        replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Veiculo") + ": ")
                                                + "&nbsp;" + this.guiaSelecionada.getVeiculoOri().replace(".0", "")));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Rota") + ": ")
                                                + "&nbsp;"
                                                + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                                        .replace("@TextoSpan", this.guiaSelecionada.getRota().replace(".0", ""))));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Chegada") + ": ")
                                                + Hora(this.guiaSelecionada.getHrCheg())));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Said") + ": ")
                                                + Hora(this.guiaSelecionada.getHrSaida())));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                        .replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                        .replace("@IdTabela", "dadosOrigem").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                                //destino
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; ")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("DadosDestino"))));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "border-bottom: 1px solid black; ")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Destinatario") + ": ")
                                                + this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                        .replace("@TextoSpan", this.guiaSelecionada.getAgSBD() + " " + this.guiaSelecionada.getnRedDst())));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Endereco") + ": " + this.guiaSelecionada.getEndDst())));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                        .replace("@IdTabela", "destino").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                                //enderecoDestino
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.guiaSelecionada.getBairroDst() + ", "
                                                + this.guiaSelecionada.getCidadeDst() + "/" + this.guiaSelecionada.getEstadoDst()));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                        .replace("@IdTabela", "enderecoDestino").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                                //dadosDestino
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; ")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Data") + ": ")
                                                + "&nbsp;" + Data(this.guiaSelecionada.getDtEntrega())));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; ")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Veiculo") + ": ")
                                                + "&nbsp;" + this.guiaSelecionada.getVeiculoDst().replace(".0", "")));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Chegada") + ": ")
                                                + Hora(this.guiaSelecionada.getHrCheg_E())));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Said") + ": ")
                                                + Hora(this.guiaSelecionada.getHrCheg_S())));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                        .replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                        .replace("@IdTabela", "dadosDestino").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                                //detalhes
                                guiaImpressaAuxTextoLinha = new StringBuilder();
                                guiaImpressaAuxTextoColuna = new StringBuilder();

                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "border-top: 1px solid black; ")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("DiscriminacaoValorIdentificacao"))));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "border-bottom: 1px solid black; ")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "border-top: 1px solid black; ")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("ValorDeclarado") + ": ")
                                                + "&nbsp;"
                                                + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                                        .replace("@TextoSpan", Moeda(this.guiaSelecionada.getValor(), this.guiaSelecionada.getMoeda()))));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                        .replace("@WidthTD", "").replace("@ClassTD", "preencheLinha")
                                        .replace("@TextoTD", getValorExtensoS(this.guiaSelecionada.getValor(), this.guiaSelecionada.getMoeda())));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "border-top: 1px solid black; ")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", getMessageS("IdentificacaoMalote"))));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                        .replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; ")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Lacres") + ": ")));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                List<TesSaidasDN> composicoesDN = new ArrayList<>();
                                boolean ATM = this.guiaSelecionada.getCodCliDst() != null
                                        && this.guiaSelecionada.getCodCliDst().length() == 7
                                        && this.guiaSelecionada.getCodCliDst().charAt(3) == '9',
                                 inserirComposicao;

                                if (ATM) {
                                    composicoesDN = this.tesSaidasDNDao.listaComposicoesATM(this.guiaSelecionada.getGuia(), this.guiaSelecionada.getSerie(), true, this.persistencia);
                                    if (composicoesDN.isEmpty()) {
                                        composicoesDN = this.tesSaidasDNDao.listaComposicoesATM(this.guiaSelecionada.getGuia(), this.guiaSelecionada.getSerie(), false, this.persistencia);
                                    }
                                }

                                List<CxFGuiasVol> ll = this.guiasweb.listarLacres(this.guiaSelecionada.getGuia(), this.guiaSelecionada.getSerie(), this.persistencia);
                                for (CxFGuiasVol lacre : ll) {
                                    guiaImpressaAuxTextoColuna = new StringBuilder();
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                            .replace("@WidthTD", "").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;")
                                                    .replace("@ClassSpan", "").replace("@TextoSpan", lacre.getLacre())));
                                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                    if (null != lacre.getObs() && !lacre.getObs().equals("")) {
                                        guiaImpressaAuxTextoColuna = new StringBuilder();
                                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 13px")
                                                        .replace("@ClassSpan", "").replace("@TextoSpan", "RegAnt: " + lacre.getObs())));
                                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                                    }

                                    if (!composicoesDN.isEmpty()) {

                                        inserirComposicao = true;

                                        TesSaidasDN tesSaidasDN;
                                        String cedula;
                                        for (int i = 0; i < composicoesDN.size(); i++) {
                                            if (inserirComposicao) {
                                                inserirComposicao = false;
                                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "text-align: center;")
                                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").
                                                                replace("@TextoSpan", getMessageS("Composicoes") + ": ")));
                                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                                            }

                                            tesSaidasDN = composicoesDN.get(i);
                                            if (tesSaidasDN.getLacre().equals(lacre.getLacre())) {

                                                switch (PreencheEsquerda(tesSaidasDN.getCodigo(), 3, "0")) {
                                                    case "001":
                                                        cedula = Moeda("1");
                                                        break;
                                                    case "002":
                                                    case "102":
                                                        cedula = Moeda("2");
                                                        break;
                                                    case "005":
                                                    case "105":
                                                        cedula = Moeda("5");
                                                        break;
                                                    case "010":
                                                    case "011":
                                                        cedula = Moeda("10");
                                                        break;
                                                    case "020":
                                                    case "021":
                                                        cedula = Moeda("20");
                                                        break;
                                                    case "050":
                                                    case "051":
                                                        cedula = Moeda("50");
                                                        break;
                                                    case "100":
                                                    case "101":
                                                        cedula = Moeda("100");
                                                        break;
                                                    default:
                                                        cedula = Moeda("0");
                                                }

                                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                                        .replace("@TextoTD", "K7 P" + (i + 1) + ":&emsp;&emsp;"
                                                                + cedula + " x " + tesSaidasDN.getQtde().replace(".0", "")));
                                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                                            }
                                        }
                                    }
                                }

                                if (this.guiaSelecionada.getER().toUpperCase().equals("E") && !ATM) {
                                    composicoesDN = this.tesSaidasDNDao.listaComposicoes(this.guiaSelecionada.getGuia(), this.guiaSelecionada.getSerie(), this.persistencia);
                                    List<TesSaidasDD> composicoesDD = this.tesSaidasDDDao.listaComposicoes(this.guiaSelecionada.getGuia(), this.guiaSelecionada.getSerie(), this.persistencia);
                                    List<TesSaidasMD> composicoesMD = this.tesSaidasMDDao.listaComposicoes(this.guiaSelecionada.getGuia(), this.guiaSelecionada.getSerie(), this.persistencia);

                                    if (!composicoesDN.isEmpty() || !composicoesDD.isEmpty() || !composicoesMD.isEmpty()) {

                                        guiaImpressaAuxTextoColuna = new StringBuilder();
                                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").
                                                        replace("@TextoSpan", getMessageS("Composicoes") + ": ")));
                                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                        StringBuilder composicoes;

                                        if (!composicoesDN.isEmpty()) {

                                            composicoes = new StringBuilder();
                                            composicoes.append("DH - ");

                                            for (TesSaidasDN tesSaidasDN : composicoesDN) {
                                                composicoes.append(FuncoesString.PreencheEsquerda(tesSaidasDN.getCodigo(), 3, "0"))
                                                        .append("x ")
                                                        .append(tesSaidasDN.getQtde().replace(".0", "")).append("\t");
                                            }

                                            guiaImpressaAuxTextoColuna = new StringBuilder();
                                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px")
                                                            .replace("@ClassSpan", "").replace("@TextoSpan", composicoes.toString())));
                                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                                        }

                                        if (!composicoesDD.isEmpty()) {

                                            composicoes = new StringBuilder();
                                            composicoes.append("DD - ");

                                            for (TesSaidasDD tesSaidasDD : composicoesDD) {
                                                composicoes.append(FuncoesString.PreencheEsquerda(tesSaidasDD.getCodigo(), 3, "0"))
                                                        .append("x ")
                                                        .append(tesSaidasDD.getQtde().replace(".0", "")).append("\t");
                                            }

                                            guiaImpressaAuxTextoColuna = new StringBuilder();
                                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px")
                                                            .replace("@ClassSpan", "").replace("@TextoSpan", composicoes.toString())));
                                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                                        }

                                        if (!composicoesMD.isEmpty()) {

                                            composicoes = new StringBuilder();
                                            composicoes.append("MD - ");

                                            for (TesSaidasMD tesSaidasMD : composicoesMD) {
                                                composicoes.append(FuncoesString.PreencheEsquerda(tesSaidasMD.getCodigo(), 3, "0"))
                                                        .append("x ")
                                                        .append(tesSaidasMD.getQtde().replace(".0", "")).append("\t");
                                            }

                                            guiaImpressaAuxTextoColuna = new StringBuilder();
                                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px")
                                                            .replace("@ClassSpan", "").replace("@TextoSpan", composicoes.toString())));
                                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                                        }
                                    }
                                }

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; text-align: justify; padding-right: 4px !important;")
                                        .replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", "&emsp; " + getMessageS("TermoAssGuia"))));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                        .replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; border-right: 1px solid black ;vertical-align: baseline;")
                                        .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", getMessageS("AssRemetente") + ": ")));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; vertical-align: baseline;")
                                        .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", getMessageS("AssDestinatario") + ": ")));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-right: 1px solid black ;vertical-align: baseline;")
                                        .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                        .replace("@TextoTD", "<br>"));
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-right: 1px solid black ;vertical-align: baseline;")
                                        .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                        .replace("@TextoTD", "<br>"));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                if (this.guiaSelecionada.getER().toUpperCase().equals("E")) {
                                    guiaImpressaAuxTextoColuna = new StringBuilder();
                                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                            .replace("@StyleTD", "border-right: 1px solid black ;vertical-align: baseline;")
                                            .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                            .replace("@TextoTD", this.guiaSelecionada.getAssinatura()));
                                    if (this.persistencia.getEmpresa().contains("SATGLOVAL")
                                            || this.persistencia.getEmpresa().contains("SPM")
                                            || this.persistencia.getEmpresa().contains("SASW")) {
                                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                                .replace("@StyleTD", "border-right: 1px solid black ;vertical-align: baseline;text-align: center;")
                                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                                .replace("@TextoTD",
                                                        "<img src=\"https://mobile.sasw.com.br:9091/satellite/assinaturas/" + this.persistencia.getEmpresa()
                                                        + "/" + this.guiaSelecionada.getSequencia().replace(".0", "") + "/" + this.guiaSelecionada.getParada()
                                                        + ".png\" height=\"47px\" width=\"59px\" alt=\"" + this.guiaSelecionada.getAssinaturaDestino() + "\"/>"));
                                    } else {
                                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                                .replace("@StyleTD", "border-right: 1px solid black ;vertical-align: baseline;text-align: center;")
                                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                                .replace("@TextoTD", this.guiaSelecionada.getAssinaturaDestino()));
                                    }
                                } else {
                                    guiaImpressaAuxTextoColuna = new StringBuilder();
                                    if (this.persistencia.getEmpresa().contains("SATGLOVAL")
                                            || this.persistencia.getEmpresa().contains("SPM")
                                            || this.persistencia.getEmpresa().contains("SASW")) {
                                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                                .replace("@StyleTD", "border-right: 1px solid black ;vertical-align: baseline;text-align: center;")
                                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                                .replace("@TextoTD",
                                                        "<img src=\"https://mobile.sasw.com.br:9091/satellite/assinaturas/" + this.persistencia.getEmpresa()
                                                        + "/" + this.guiaSelecionada.getSequencia().replace(".0", "") + "/" + this.guiaSelecionada.getParada()
                                                        + ".png\" height=\"47px\" width=\"59px\" alt=\"" + this.guiaSelecionada.getAssinatura() + "\"/>"));
                                    } else {
                                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                                .replace("@StyleTD", "border-right: 1px solid black ;vertical-align: baseline;text-align: center;")
                                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                                .replace("@TextoTD", this.guiaSelecionada.getAssinatura()));
                                    }
                                    if (this.persistencia.getEmpresa().contains("SATGLOVAL")
                                            || this.persistencia.getEmpresa().contains("SPM")
                                            || this.persistencia.getEmpresa().contains("SASW")) {
                                        if (this.guiaSelecionada.getParada_E() != null && !this.guiaSelecionada.getParada_E().equals("")
                                                && this.guiaSelecionada.getSequencia_E() != null && !this.guiaSelecionada.getSequencia_E().equals("")) {
                                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                                    .replace("@StyleTD", "border-right: 1px solid black ;vertical-align: baseline;text-align: center;")
                                                    .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                                    .replace("@TextoTD",
                                                            "<img src=\"https://mobile.sasw.com.br:9091/satellite/assinaturas/" + this.persistencia.getEmpresa()
                                                            + "/" + this.guiaSelecionada.getSequencia_E().replace(".0", "") + "/" + this.guiaSelecionada.getParada_E()
                                                            + ".png\" height=\"47px\" width=\"59px\" alt=\"" + this.guiaSelecionada.getAssinaturaDestino() + "\"/>"));
                                        } else {
                                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                                    .replace("@StyleTD", "border-right: 1px solid black ;vertical-align: baseline;text-align: center;")
                                                    .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                                    .replace("@TextoTD", this.guiaSelecionada.getAssinaturaDestino()));
                                        }
                                    } else {
                                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                                .replace("@StyleTD", "border-right: 1px solid black ;vertical-align: baseline;text-align: center;")
                                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                                .replace("@TextoTD", this.guiaSelecionada.getAssinaturaDestino()));
                                    }
                                }
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "border-bottom: 1px solid black; ")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; ").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", getMessageS("Autenticacao"))));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", tratarAutenticacao(this.guiaSelecionada.getCnpjFilial(),
                                                        this.guiaSelecionada.getGuia().replace(".0", ""),
                                                        this.guiaSelecionada.getSerie(),
                                                        this.guiaSelecionada.getSequencia().replace(".0", ""),
                                                        this.guiaSelecionada.getParada())) + "&nbsp;"));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoColuna = new StringBuilder();
                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                        .replace("@StyleTD", "border-top: 1px solid black; border-bottom: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", getMessageS("Obs") + ": ")
                                                + "&nbsp;"
                                        //                                                + this.guiaSelecionada.getObserv()
                                        ));
                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                        .replace("@StyleTR", "")
                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                                guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left;"
                                        + " width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;"
                                        + "border-left: 1px solid black;font-size: 14px; margin-bottom: 10px;")
                                        .replace("@IdTabela", "detalhes").replace("@ClassTabela", "")
                                        .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        }

                        guiaImpressa.append(guiaImpressaAuxTextoTabela);
//                this.html = guiaImpressa.toString();
                        this.html = this.relatorio.replace("@TitleRelatorio", this.guia.getGuia().toBigInteger().toString())
                                .replace("@RelatorioHTML", guiaImpressa.toString());
                        PrimeFaces.current().executeScript("PF('dlgImprimir').show();");
                    } catch (Exception e) {
                        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
                        FacesContext.getCurrentInstance().addMessage(null, mensagem);
                        this.log = this.getClass().getSimpleName() + "\r\n"
                                + Thread.currentThread().getStackTrace()[1].getMethodName()
                                + "\r\n" + e.getMessage() + "\r\n";
                        this.logerro.Grava(this.log, this.caminho);
                    }
                }
                break;
            case 2:
                if (null == this.preOrderSelecionado) {
                    FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecionePedido"), null);
                    FacesContext.getCurrentInstance().addMessage(null, mensagem);
                } else {
                    try {

                        this.nomeArquivo = "remessa" + this.preOrderSelecionado.getPedidoCliente() + ".pdf";

                        Filiais infoFilial = this.guiasweb.buscaInfoFilial(this.preOrderSelecionado.getCodFil(), this.persistencia);

                        this.preOrderDetalhado = this.guiasweb.detalhesPreOrder(this.preOrderSelecionado.getPedidoCliente(), this.persistencia);

                        this.relatorio = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/relatorio.html"));
                        this.tabela = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/tabela.html"));
                        this.linha = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/linha.html"));
                        this.coluna = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/coluna.html"));
                        this.span = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/span.html"));

                        StringBuilder guiaImpressa = new StringBuilder();
                        StringBuilder guiaImpressaAuxTextoTabela, guiaImpressaAuxTextoLinha, guiaImpressaAuxTextoColuna;

                        guiaImpressaAuxTextoTabela = new StringBuilder();
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "4").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", "<img src=\"" + Logos.getLogo(this.persistencia.getEmpresa(), this.preOrderSelecionado.getCodFil())
                                        + "\" height=\"47px\" width=\"59px\"/>"));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", infoFilial.getRazaoSocial())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", infoFilial.getEndereco()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", infoFilial.getCidade() + "&nbsp;-&nbsp;"
                                        + CEP(infoFilial.getCEP())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("CGC"))
                                        + ": &nbsp;" + CNPJ(infoFilial.getCNPJ())
                                        + this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Telefone"))
                                        + ": &nbsp;" + Fone(infoFilial.getFone())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "4").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD",
                                        this.span.replace("@StyleSpan", "font-weight: bold; font-size: 18px;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", "PEDIDO DE TRANSPORTE DE VALORES COM IDENTIFICAÇÃO DE GTV/eGTV")));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 500px !important; background: #fff; overflow: hidden; width: 500px;  font-size: 16px; padding: 3px 3px; color: #000000; font-size: 16px; font-weight: normal;")
                                .replace("@IdTabela", "cabecalho").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //filialOS
                        guiaImpressaAuxTextoLinha = new StringBuilder();

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "width: 100%;")
                                .replace("@WidthTD", "100%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Filial"))
                                        + ": &nbsp;" + PreencheEsquerda(this.preOrderSelecionado.getCodFil().replace(".0", ""), 4, "0")));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "2").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: bold;font-size: 20px;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", this.preOrderSelecionado.getPedidoCliente())));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("OS"))
                                        + ": &nbsp;" + this.preOrderSelecionado.getOS().replace(".0", "")));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela
                                .replace("@StyleTabela", "border-collapse: collapse; text-align: left; background: #fff; overflow: hidden; width: 500px;  font-size: 16px; padding: 3px 3px; color: #000000; font-weight: normal;")
                                .replace("@IdTabela", "cabecalho").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //cliente
//                        guiaImpressaAuxTextoLinha = new StringBuilder();
//                        guiaImpressaAuxTextoColuna = new StringBuilder();
//
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "border-top: 1px solid black;")
//                                .replace("@WidthTD", "").replace("@ClassTD", "")
//                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;")
//                                        .replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("ClienteP") + ": ")));
//                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
//                                .replace("@StyleTR", "")
//                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                        guiaImpressaAuxTextoColuna = new StringBuilder();
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "border-top: 1px solid black;")
//                                .replace("@WidthTD", "").replace("@ClassTD", "")
//                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "")
//                                        .replace("@TextoSpan", this.preOrderSelecionado.getNRedFat())));
//                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                        guiaImpressaAuxTextoColuna = new StringBuilder();
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
//                                .replace("@WidthTD", "").replace("@ClassTD", "")
//                                .replace("@TextoTD", this.span.replace("@StyleSpan", "").replace("@ClassSpan", "italic").replace("@TextoSpan", getMessageS("Endereco"))
//                                        + ": &nbsp;" + this.preOrderSelecionado.getEndeFat()));
//                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                        guiaImpressaAuxTextoColuna = new StringBuilder();
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
//                                .replace("@WidthTD", "").replace("@ClassTD", "")
//                                .replace("@TextoTD", this.preOrderSelecionado.getBaiFat() + ", " 
//                                        + this.preOrderSelecionado.getCidFat() + "/" + this.preOrderSelecionado.getUFFat()));
//                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                        guiaImpressaAuxTextoColuna = new StringBuilder();
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").replace("@StyleTD", "")
//                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
//                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("CGC"))
//                                        + ": &nbsp;" + CNPJ(this.preOrderSelecionado.getCGCFat())));
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").replace("@StyleTD", "")
//                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
//                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("InscEstadual"))
//                                        + ": &nbsp;" + this.preOrderSelecionado.getIEFat()));
//                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
//                                .replace("@IdTabela", "cliente").replace("@ClassTabela", "")
//                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));
                        //origem
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", getMessageS("DadosOrigem"))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Remetente") + ": ")
                                        + " " + this.span.replace("@StyleSpan", "font-size: 16px;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", this.preOrderSelecionado.getAgenciaOri()
                                                        + " " + this.preOrderSelecionado.getSubAgenciaOri() + " " + this.preOrderSelecionado.getNRed1())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Endereco") + ": ")
                                        + " " + this.preOrderSelecionado.getEndeOri()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "origem").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //enderecoOrigem
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.preOrderSelecionado.getBaiOri() + ", "
                                        + this.preOrderSelecionado.getCidOri() + "/" + this.preOrderSelecionado.getUFOri()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "enderecoOrigem").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //dadosOrigem
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Data") + ": ")
                                        + "&nbsp;"
                                        + Data(this.preOrderSelecionado.getColetaOri())));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1").
                                replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Veiculo") + ": ")
                                        + "&nbsp;"
                                        + this.preOrderSelecionado.getVeiculoOri().replace(".0", "")));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Rota") + ": ")
                                        + "&nbsp;"
                                        + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", this.preOrderSelecionado.getRotaOri().replace(".0", ""))));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Chegada") + ": ")
                                        + Hora(this.preOrderSelecionado.getHora1())));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center; border-top: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Said") + ": ")
                                        + Hora(this.preOrderSelecionado.getHora2())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "dadosOrigem").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //destino
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("DadosDestino"))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "border-bottom: 1px solid black; ")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Destinatario") + ": ")
                                        + this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", this.preOrderSelecionado.getAgencia() + " "
                                                        + this.preOrderSelecionado.getSubAgencia() + " " + this.preOrderSelecionado.getNRed2())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", getMessageS("Endereco") + ": ")
                                        + this.span.replace("@StyleSpan", "").replace("@ClassSpan", "")
                                                .replace("@TextoSpan", this.preOrderSelecionado.getEndeDst())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "destino").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //enderecoDestino
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.preOrderSelecionado.getBaiDst() + ", "
                                        + this.preOrderSelecionado.getCidDst() + "/" + this.preOrderSelecionado.getUFDst()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "enderecoDestino").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //dadosDestino
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Data") + ": ")
                                        + "&nbsp;" + Data(this.preOrderSelecionado.getColetaDst())));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Veiculo") + ": ")
                                        + "&nbsp;" + this.preOrderSelecionado.getVeiculoDst().replace(".0", "")));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Chegada") + ": ")
                                        + Hora(this.preOrderSelecionado.getHoraChegada())));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Said") + ": ")
                                        + Hora(this.preOrderSelecionado.getHoraSaida())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "dadosDestino").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        //detalhes
                        guiaImpressaAuxTextoLinha = new StringBuilder();
                        guiaImpressaAuxTextoColuna = new StringBuilder();

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("DiscriminacaoValorIdentificacao"))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "border-bottom: 1px solid black; ")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("ValorDeclarado") + ": ")
                                        + "&nbsp;"
                                        + this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;")
                                                .replace("@ClassSpan", "").replace("@TextoSpan", Moeda(this.preOrderSelecionado.getValor()))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                .replace("@WidthTD", "").replace("@ClassTD", "preencheLinha")
                                .replace("@TextoTD", getValorExtensoS(this.preOrderSelecionado.getValor())));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", getMessageS("IdentificacaoMalote"))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; ")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Lacres") + ": ")));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        for (PreOrder lacre : this.preOrderDetalhado) {
                            guiaImpressaAuxTextoColuna = new StringBuilder();
                            guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
                                    .replace("@WidthTD", "").replace("@ClassTD", "")
                                    .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;")
                                            .replace("@ClassSpan", "").replace("@TextoSpan", lacre.getPreOrderVolLacre() + " - "
                                            + Moeda(lacre.getValor()) + ". ")
                                            + this.span.replace("@StyleSpan", "font-size: 13px;").replace("@ClassSpan", "")
                                                    .replace("@TextoSpan", getMessageS("Guia") + ": " + lacre.getGuia() + "/" + lacre.getSerie())));
                            guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                    .replace("@TextoTR", guiaImpressaAuxTextoColuna));

//                            if (null != lacre.getObs() && !lacre.getObs().equals("")) {
//                                guiaImpressaAuxTextoColuna = new StringBuilder();
//                                guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1").replace("@StyleTD", "")
//                                        .replace("@WidthTD", "").replace("@ClassTD", "")
//                                        .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 13px")
//                                                .replace("@ClassSpan", "").replace("@TextoSpan", "RegAnt: " + lacre.getObs())));
//                                guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
//                                        .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//                            }
                        }

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; text-align: justify; padding-right: 4px !important;")
                                .replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", "&emsp; " + getMessageS("TermoAssGuia"))));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
                                .replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; border-right: 1px solid black ;vertical-align: baseline;")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("AssRemetente") + ": ")));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; vertical-align: baseline;")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("AssDestinatario") + ": ")));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-right: 1px solid black ;vertical-align: baseline;")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", "<br>"));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-right: 1px solid black ;vertical-align: baseline;")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", "<br>"));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-right: 1px solid black ;vertical-align: baseline;")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.preOrderSelecionado.getAssinatura()));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "vertical-align: baseline;text-align: center;")
                                .replace("@WidthTD", "50%").replace("@ClassTD", "")
                                .replace("@TextoTD", this.preOrderSelecionado.getAssinaturaDestino()));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "border-bottom: 1px solid black; ")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "border-top: 1px solid black; ").replace("@WidthTD", "").replace("@ClassTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "")
                                        .replace("@TextoSpan",
                                                //                                                getMessageS("Autenticacao")
                                                ""
                                        )));
                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

//                        guiaImpressaAuxTextoColuna = new StringBuilder();
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "").replace("@WidthTD", "").replace("@ClassTD", "")
//                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
//                                        .replace("@TextoSpan", xxx.getAutenticacao()) + "&nbsp;"));
//                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
//                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
//
//                        guiaImpressaAuxTextoColuna = new StringBuilder();
//                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
//                                .replace("@StyleTD", "border-top: 1px solid black; border-bottom: 1px solid black;").replace("@WidthTD", "").replace("@ClassTD", "")
//                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Obs") + ": ")
//                                        + "&nbsp;"
//                                        + xxx.getObserv()));
//                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "")
//                                .replace("@StyleTR", "")
//                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));
                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@StyleTabela", "border-collapse: collapse; text-align: left; width: 100%;background: #fff;overflow: hidden; width: 500px;border-right: 1px solid black;border-left: 1px solid black;font-size: 14px;")
                                .replace("@IdTabela", "detalhes").replace("@ClassTabela", "")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        guiaImpressa.append(guiaImpressaAuxTextoTabela);
//                this.html = guiaImpressa.toString();
                        this.html = this.relatorio.replace("@TitleRelatorio", this.preOrderSelecionado.getPedidoCliente())
                                .replace("@RelatorioHTML", guiaImpressa.toString());
                        PrimeFaces.current().executeScript("PF('dlgImprimir').show();");
                    } catch (Exception e) {
                        FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
                        FacesContext.getCurrentInstance().addMessage(null, mensagem);
                        this.log = this.getClass().getSimpleName() + "\r\n"
                                + Thread.currentThread().getStackTrace()[1].getMethodName()
                                + "\r\n" + e.getMessage() + "\r\n";
                        this.logerro.Grava(this.log, this.caminho);
                    }
                }
                break;
            default:

        }
    }

    public void gerarGuiaDownload() throws Exception {
        try {
            InputStream stream = new ByteArrayInputStream(this.html
                    .replaceAll("https://mobile.sasw.com.br:9091", "file:///C:/xampp/htdocs")
                    .replaceAll("https://spm.cashctr.com:9091", "file:///C:/xampp/htdocs").getBytes());

            ByteArrayOutputStream osPdf = new ByteArrayOutputStream();
            ITextRenderer renderer = new ITextRenderer();
            Tidy tidy = new Tidy();
            tidy.setShowWarnings(false);
            Document doc = tidy.parseDOM(stream, null);
            renderer.setDocument(doc, null);
            renderer.layout();
            renderer.createPDF(osPdf);

            InputStream inputPDF = new ByteArrayInputStream(osPdf.toByteArray());

            this.arquivoDownload = new DefaultStreamedContent(inputPDF, "pdf", this.nomeArquivo);
            osPdf.close();
            stream.close();
            inputPDF.close();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    public void listarManifestosDisponiveis(ActionEvent acitionEvent) {
        try {
//            this.preOrderSelecionado = null;
            this.pedidosRecentes = this.guiasweb.listarManifestosDisponiveis(this.codFil.equals(BigDecimal.ZERO) ? "" : this.codFil,
                    this.data1, this.data2, this.persistencia);
            if (this.pedidosRecentes.isEmpty()) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SemManifestos"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else if (this.pedidosRecentes.size() == 1) {
                this.preOrderSelecionado = this.pedidosRecentes.get(0);
                listarManifestoPreOrder();
            } else {
                PrimeFaces.current().resetInputs("formManifestosDisponiveis");
                PrimeFaces.current().executeScript("PF('dlgManifestosDisponiveis').show()");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void listarManifestoPreOrder() {
        try {
            if (null == this.preOrderSelecionado) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneManifesto"), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            } else {
                this.manifesto = this.guiasweb.listarManifestosPreOrder(this.preOrderSelecionado.getCodFil(),
                        this.preOrderSelecionado.getDtColeta(), this.preOrderSelecionado.getLote(), this.persistencia);
                if (this.manifesto.isEmpty()) {
                    FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SemManifesto"), null);
                    FacesContext.getCurrentInstance().addMessage(null, message);
                } else {

                    this.nomeArquivo = "manifesto" + this.preOrderSelecionado.getDtColeta() + ".pdf";

                    Filiais infoFilial = this.guiasweb.buscaInfoFilial(this.preOrderSelecionado.getCodFil(), this.persistencia);

                    this.relatorio = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/relatorio.html"));
                    this.tabela = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/tabela.html"));
                    this.linha = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/linha.html"));
                    this.coluna = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/coluna.html"));
                    this.span = LerArquivo.obterConteudo(GuiasClienteMB.class.getResourceAsStream("relatorio/span.html"));

                    StringBuilder guiaImpressa = new StringBuilder();
                    StringBuilder guiaImpressaAuxTextoTabela, guiaImpressaAuxTextoLinha, guiaImpressaAuxTextoColuna;

                    guiaImpressaAuxTextoLinha = new StringBuilder();
                    guiaImpressaAuxTextoColuna = new StringBuilder();
                    guiaImpressaAuxTextoTabela = new StringBuilder();

                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "4").replace("@StyleTD", "")
                            .replace("@WidthTD", "").replace("@ClassTD", "")
                            .replace("@TextoTD", "<img src=\"" + Logos.getLogo(this.persistencia.getEmpresa(), this.preOrderSelecionado.getCodFil())
                                    + "\" height=\"47px\" width=\"59px\"/>"));
                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                            .replace("@WidthTD", "").replace("@ClassTD", "")
                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-size: 15px;font-weight: bold;").replace("@ClassSpan", "")
                                    .replace("@TextoSpan", infoFilial.getRazaoSocial())));
                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                    guiaImpressaAuxTextoColuna = new StringBuilder();
                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1").replace("@StyleTD", "")
                            .replace("@WidthTD", "").replace("@ClassTD", "")
                            .replace("@TextoTD", infoFilial.getEndereco()));
                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center")
                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                    guiaImpressaAuxTextoColuna = new StringBuilder();
                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                            .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                            .replace("@TextoTD", infoFilial.getCidade() + "&nbsp;-&nbsp;"
                                    + CEP(infoFilial.getCEP())));
                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                    guiaImpressaAuxTextoColuna = new StringBuilder();
                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "3").replace("@RowspanTD", "1")
                            .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("CGC"))
                                    + ": &nbsp;" + CNPJ(infoFilial.getCNPJ())
                                    + this.span.replace("@StyleSpan", "font-style: italic;font-size: 14px;").replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Telefone"))
                                    + ": &nbsp;" + Fone(infoFilial.getFone())));
                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                    guiaImpressaAuxTextoColuna = new StringBuilder();
                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "4").replace("@RowspanTD", "1")
                            .replace("@StyleTD", "text-align: center").replace("@WidthTD", "").replace("@ClassTD", "")
                            .replace("@TextoTD",
                                    this.span.replace("@StyleSpan", "font-weight: bold; font-size: 18px;").replace("@ClassSpan", "")
                                            .replace("@TextoSpan", "M A N I F E S T O")));
                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                    guiaImpressaAuxTextoTabela.append(this.tabela
                            .replace("@StyleTabela", "border-collapse: collapse; text-align: left; background: #fff; overflow: hidden; width: 500px;  font-size: 16px; padding: 3px 3px; color: #000000; font-weight: normal;")
                            .replace("@IdTabela", "cabecalho").replace("@ClassTabela", "")
                            .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

//            guiaImpressa.append(guiaImpressaAuxTextoTabela);
                    BigDecimal valorTotal = BigDecimal.ZERO;
                    for (PreOrderManifesto preOrderManifesto : this.manifesto) {
                        guiaImpressaAuxTextoLinha = new StringBuilder();

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", "<hr>")));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "")
                                .replace("@TextoTD", getMessageS("Destino") + ": "));

                        String TextoDet = "";

                        if (null != preOrderManifesto.getAgenciaDestino()
                                && !preOrderManifesto.getAgenciaDestino().equals("")
                                && null != preOrderManifesto.getAgenciaDestino()
                                && !preOrderManifesto.getAgenciaDestino().equals("")) {
                            TextoDet = preOrderManifesto.getAgenciaDestino() + "/" + preOrderManifesto.getSubAgenciaDestino() + " " + preOrderManifesto.getDestino();
                        } else if (null != preOrderManifesto.getAgenciaDestino()
                                && !preOrderManifesto.getAgenciaDestino().equals("")) {
                            TextoDet = preOrderManifesto.getAgenciaDestino() + " " + preOrderManifesto.getDestino();
                        } else if (null != preOrderManifesto.getSubAgenciaDestino()
                                && !preOrderManifesto.getSubAgenciaDestino().equals("")) {
                            TextoDet = preOrderManifesto.getSubAgenciaDestino() + " - " + preOrderManifesto.getDestino();
                        } else {
                            TextoDet = preOrderManifesto.getDestino();
                        }

                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", TextoDet)));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "")
                                .replace("@TextoTD", getMessageS("Valor") + ": "));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", Moeda(preOrderManifesto.getValor()))));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "")
                                .replace("@TextoTD", getMessageS("Lacre") + ": "));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", preOrderManifesto.getLacre())));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "")
                                .replace("@TextoTD", getMessageS("Guia") + ": "));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", preOrderManifesto.getGuia() + " - " + preOrderManifesto.getSerie())));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "")
                                .replace("@TextoTD", getMessageS("AssinadoPor") + ": "));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", preOrderManifesto.getCodPessoaAut() + " " + preOrderManifesto.getNome())));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoColuna = new StringBuilder();
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "")
                                .replace("@TextoTD", getMessageS("Rota") + ": "));
                        guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                                .replace("@StyleTD", "").replace("@WidthTD", "")
                                .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                        .replace("@TextoSpan", preOrderManifesto.getRota())));

                        guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                                .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                        guiaImpressaAuxTextoTabela.append(this.tabela.replace("@IdTabela", "").replace("@ClassTabela", "")
                                .replace("@StyleTabela", " background: #fff; width: 500px;")
                                .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                        valorTotal = valorTotal.add(new BigDecimal(preOrderManifesto.getValor()));
                    }

                    guiaImpressaAuxTextoLinha = new StringBuilder();

                    guiaImpressaAuxTextoColuna = new StringBuilder();
                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                            .replace("@StyleTD", "").replace("@WidthTD", "")
                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                    .replace("@TextoSpan", "<hr>")));

                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                    guiaImpressaAuxTextoColuna = new StringBuilder();
                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                            .replace("@StyleTD", "").replace("@WidthTD", "")
                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold")
                                    .replace("@ClassSpan", "").replace("@TextoSpan", getMessageS("Total").toUpperCase())));
                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "text-align: center; font-size: 16px")
                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                    guiaImpressaAuxTextoColuna = new StringBuilder();
                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                            .replace("@StyleTD", "").replace("@WidthTD", "")
                            .replace("@TextoTD", getMessageS("Quantidade") + ": "));
                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                            .replace("@StyleTD", "").replace("@WidthTD", "")
                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                    .replace("@TextoSpan", this.manifesto.size() + "")));

                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                    guiaImpressaAuxTextoColuna = new StringBuilder();
                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                            .replace("@StyleTD", "").replace("@WidthTD", "")
                            .replace("@TextoTD", getMessageS("Valor") + ": "));
                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "1").replace("@RowspanTD", "1")
                            .replace("@StyleTD", "").replace("@WidthTD", "")
                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                    .replace("@TextoSpan", Moeda(valorTotal.toString()))));

                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                    guiaImpressaAuxTextoColuna = new StringBuilder();
                    guiaImpressaAuxTextoColuna.append(this.coluna.replace("@ColspanTD", "2").replace("@RowspanTD", "1")
                            .replace("@StyleTD", "").replace("@WidthTD", "")
                            .replace("@TextoTD", this.span.replace("@StyleSpan", "font-weight: bold").replace("@ClassSpan", "")
                                    .replace("@TextoSpan", getValorExtensoS(valorTotal.toString()))));

                    guiaImpressaAuxTextoLinha.append(this.linha.replace("@ClassTR", "").replace("@StyleTR", "")
                            .replace("@TextoTR", guiaImpressaAuxTextoColuna));

                    guiaImpressaAuxTextoTabela.append(this.tabela.replace("@IdTabela", "").replace("@ClassTabela", "")
                            .replace("@StyleTabela", "width:500px; background: #fff;")
                            .replace("@TextoTabela", guiaImpressaAuxTextoLinha));

                    guiaImpressa.append(guiaImpressaAuxTextoTabela);

                    this.html = guiaImpressa.toString();
                    PrimeFaces.current().executeScript("PF('dlgImprimir').show();");
                }
            }
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void pesquisar() {
        try {
            if (this.dataSelecionada1.after(this.dataSelecionada2)) {
                throw new Exception("IntervaloInvalido");
            }

            this.data1 = this.dataSelecionada1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            this.data2 = this.dataSelecionada2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            this.datasSelecionadas.get(0).setTime(this.dataSelecionada1.getTime());
            this.datasSelecionadas.get(1).setTime(this.dataSelecionada2.getTime());

            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabela");
            this.filters.replace("Guia", this.guiaPesquisa.getGuia().equals("") ? "" : this.guiaPesquisa.getGuia());
            this.filters.replace("Lacre", this.guiaPesquisa.getLacre().equals("") ? "" : this.guiaPesquisa.getLacre());
            this.filters.replace("DtInicio", this.data1);
            this.filters.replace("DtFim", this.data2);
            dt.setFilters(this.filters);
            getAllGuias();
            dt.setFirst(0);

            gerarLog("Contando quantidade de guias.");
            this.total = this.guiasweb.totalGuias(this.filters, this.persistencia);
            gerarLog("Contando valor total das guias.");
            this.valorGuias = this.guiasweb.valorGuias(this.filters, this.persistencia);
            gerarLog("Contando quantidade de volumes de guias.");
            this.volumesGuias = this.guiasweb.qtdVolumes(this.filters, this.persistencia);

            DataTable dtPedido = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabelaPedido");
            this.filtersPedido.replace(" PreOrder.DtColeta >= ? ", this.data1);
            this.filtersPedido.replace(" PreOrder.DtColeta <= ? ", this.data2);
            this.filtersPedido.replace(" PreOrder.Guia = ? ", this.guiaPesquisa.getGuia().equals("") ? "" : this.guiaPesquisa.getGuia());
            dtPedido.setFilters(this.filtersPedido);
            getAllPedidos();
            dtPedido.setFirst(0);

            DataTable dtExtrato = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabelaExtrato");
            this.filterExtrato.replace(" FatTvPlan.data >= ? ", this.data1);
            this.filterExtrato.replace(" FatTvPlan.data <= ? ", this.data2);
            dtExtrato.setFilters(this.filterExtrato);
            getExtrato();
            dtExtrato.setFirst(0);

            // Limpar cache das notas fiscais e extrato quando período muda
            this.notasFiscaisFiltradas = null;
            this.notasFiscais = null;
            this.extratoSimples = null;
            this.extratosFiltrados = null;

            this.preOrderSelecionado = null;
            this.guiaPesquisa = new EGtv();
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void limparFiltros() {
        try {
            this.calendar = Calendar.getInstance();

            this.calendar.setTime(Date.from(Instant.now()));
            this.data2 = this.calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            this.datasSelecionadas.get(1).setTime(this.calendar.getTimeInMillis());

            this.calendar.add(Calendar.DAY_OF_YEAR, -1);
            this.data1 = this.calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            this.datasSelecionadas.get(0).setTime(this.calendar.getTimeInMillis());

            this.dataSelecionada1 = this.datasSelecionadas.get(0);
            this.dataSelecionada2 = this.datasSelecionadas.get(1);

            this.filters = new HashMap();
            this.filters.put("DtInicio", this.data1);
            this.filters.put("DtFim", this.data2);
            this.filters.put("CodCli", this.codcli);
            this.filters.put("CodPessoa", this.codPessoa.toBigInteger().toString());
            this.filters.put("Guia", "");
            this.filters.put("Lacre", "");
            this.filters.put(" CodFil = ? ", this.codFil.equals("0") ? "" : this.codFil);

            this.filtersPedido = new HashMap();
            this.filtersPedido.put(" PreOrder.DtColeta >= ? ", this.data1);
            this.filtersPedido.put(" PreOrder.DtColeta <= ? ", this.data2);
            this.filtersPedido.put("CodCli", this.codcli);
            this.filtersPedido.put("CodPessoa", this.codPessoa.toBigInteger().toString());
            this.filtersPedido.put(" PreOrder.Guia = ? ", "");

            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabela");
            dt.setFilters(this.filters);
            getAllGuias();
            dt.setFirst(0);

            gerarLog("Contando quantidade de guias.");
            this.total = this.guiasweb.totalGuias(this.filters, this.persistencia);
            gerarLog("Contando valor total das guias.");
            this.valorGuias = this.guiasweb.valorGuias(this.filters, this.persistencia);
            gerarLog("Contando quantidade de volumes de guias.");
            this.volumesGuias = this.guiasweb.qtdVolumes(this.filters, this.persistencia);

            DataTable dtPedido = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabelaPedido");
            this.filtersPedido.replace(" PreOrder.DtColeta >= ? ", this.data1);
            this.filtersPedido.replace(" PreOrder.DtColeta <= ? ", this.data2);
            dtPedido.setFilters(this.filtersPedido);
            getAllPedidos();

            DataTable dtExtrato = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:mainTabView:tabelaExtrato");
            this.filterExtrato.replace(" FatTvPlan.data >= ? ", this.data1);
            this.filterExtrato.replace(" FatTvPlan.data <= ? ", this.data2);
            dtExtrato.setFilters(this.filterExtrato);
            getExtrato();
            dtExtrato.setFirst(0);

            this.preOrderSelecionado = null;
            this.guiaPesquisa = new EGtv();
            this.limpar = false;
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
    }

    public void verificaClienteOrigemPedido() {
        if (null == this.nomecli || this.nomecli.equals("")) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecioneUnicoCliente"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            PrimeFaces.current().executeScript("PF('dlgUpload').show();");
        }
    }

    public void preImportacaoPedido() {
        PrimeFaces.current().ajax().update("formImportarPedidos");
        PrimeFaces.current().executeScript("PF('dlgImportarPedidos').show();");
    }

    //Realiza o tramento para autenticacao  
    private String tratarAutenticacao(String cnpj, String guia, String serie, String seqRota, String parada) {
        StringBuilder autTemp = new StringBuilder();
        autTemp = autTemp.append(cnpj.replace(".", "").replace("-", "").replace("/", ""))
                .append(PreencheEsquerda(guia, 14, "0"))
                .append(PreencheEsquerda(serie, 4, "0"))
                .append(PreencheEsquerda(seqRota, 8, "0"))
                .append(PreencheEsquerda(parada, 4, "0"));

        StringBuilder sb = new StringBuilder();

        float i = 0;
        for (char s : autTemp.toString().toCharArray()) {
            if (i % 4 == 0 && i != 0 && i != 24) {
                sb.append(' ');
            }
            if (i % 24 == 0 && i != 0) {
                sb.append("</br>");
            }
            sb = sb.append(s);

            i++;
        }
        return sb.toString();
    }

    public void inserirPreOrders() {
        try {
            if (this.persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
                this.guiasweb.inserirPedidosImportados(this.listaAgencias, this.codcli, this.lotes,
                        FuncoesString.RecortaAteEspaço(this.operador, 0, 10), this.persistencia);
            } else {
                this.guiasweb.inserirPedidosImportados(this.listaAgencias, this.codcli, this.lotes, this.codFil,
                        FuncoesString.RecortaAteEspaço(this.operador, 0, 10), this.persistencia);
            }
            this.pedidosRecentes = this.guiasweb.pedidosRecentes(this.codcli, this.codFil, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgConfirmacao').hide();");
            PrimeFaces.current().executeScript("PF('dlgPreOrders').hide();");
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("ImportacaoCompleta"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void verificarExistenciaPreOrder() {
        try {
            List<String> pedidos = new ArrayList<>();
            for (BBPedidoAgencia bb : this.listaAgencias) {
                for (BBPedidoMalote malote : bb.getListaMalotes()) {
                    if (!pedidos.contains(malote.getPedidoCliente())) {
                        pedidos.add(malote.getPedidoCliente());
                    }
                }
            }
            this.lotes = this.guiasweb.verificarExistenciaPreOrder(this.listaAgencias.get(0).getDtColeta(), this.codcli, pedidos, this.persistencia);
            if (this.lotes.size() == 1) {
                this.mensagemImportacao = getMessageS("MensagemConfirmacaoPreOrder")
                        .replace("%s1", FuncoesString.formatarString(
                                LocalDate.parse(this.listaAgencias.get(0).getDtColeta(),
                                        DateTimeFormatter.ofPattern("yyyyMMdd")).format(DateTimeFormatter.ofPattern("ddMMyyyy")), "##/##/####"));

                PrimeFaces.current().executeScript("PF('dlgConfirmacao').show();");
            } else if (this.lotes.size() > 1) {
                this.mensagemImportacao = getMessageS("MensagemConfirmacaoPreOrders")
                        .replace("%s1", FuncoesString.formatarString(
                                LocalDate.parse(this.listaAgencias.get(0).getDtColeta(),
                                        DateTimeFormatter.ofPattern("yyyyMMdd")).format(DateTimeFormatter.ofPattern("ddMMyyyy")), "##/##/####"))
                        .replace("%s2", this.lotes.size() + "");

                PrimeFaces.current().executeScript("PF('dlgConfirmacao').show();");
            } else {
                inserirPreOrders();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void inserirNovoLotePreOrders() {
        this.lotes = new ArrayList<>();
        inserirPreOrders();
    }

    public void realizarUpload(FileUploadEvent event) {
        try {
            new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\pedidos\\" + getDataAtual("SQL")).mkdirs();
            String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\pedidos\\" + getDataAtual("SQL") + "\\" + event.getFile().getFileName();
            File file = new File(arquivo);
            if (file.exists()) {
                arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\pedidos\\" + getDataAtual("SQL") + "\\" + getDataAtual("HHMMSS") + event.getFile().getFileName();
                file = new File(arquivo);
            }

            FileOutputStream output = new FileOutputStream(file);
            output.write(event.getFile().getContents());
            output.close();
            this.arquivosPedidos.add(file);

            FileReader fileReader = new FileReader(file);
            BufferedReader bufferedReader = new BufferedReader(fileReader);
            String linha, dtEntrega = "", dtColeta = "";
            String[] dados;
            int indice = -1;
            BBPedidoAgencia pedidoAgencia;
            BBPedidoMalote pedidoMalote;
            this.listaAgencias = new ArrayList<>();
            this.volumesPreOrder = 0;
            while ((linha = bufferedReader.readLine()) != null) {
                this.logerro.Grava("leitura arquivo: " + linha, caminho);
                dados = linha.split("\\|");
                if (dados[0].equals("1")) {
                    dtColeta = LocalDate.parse(dados[2], DateTimeFormatter.ofPattern("ddMMyyyy")).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                    dtEntrega = LocalDate.parse(dados[4], DateTimeFormatter.ofPattern("ddMMyyyy")).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                } else if (dados[0].equals("2")) {
                    pedidoAgencia = new BBPedidoAgencia();
                    pedidoAgencia.setAgencia(dados[1]);
                    pedidoAgencia.setSubAgencia(dados[2]);
                    pedidoAgencia.setDtColeta(dtColeta);
                    pedidoAgencia.setDtEntrega(dtEntrega);
                    pedidoAgencia.setListaMalotes(new ArrayList<>());

                    indice = this.listaAgencias.indexOf(pedidoAgencia);
                    if (indice == -1) {
                        this.listaAgencias.add(pedidoAgencia);
                        indice = this.listaAgencias.indexOf(pedidoAgencia);
                    }
                } else if (dados[0].equals("3")) {
                    pedidoMalote = new BBPedidoMalote();
                    pedidoMalote.setLacre(dados[1]);
                    pedidoMalote.setHorario(dados[2]);
                    pedidoMalote.setValor(new BigDecimal(dados[4]).divide(new BigDecimal("100")).toString());
                    pedidoMalote.setObs(dados[17]);
                    pedidoMalote.setPedidoCliente(dados[17].substring(12, 20));

                    if (indice != -1) {
                        this.volumesPreOrder++;
                        this.listaAgencias.get(indice).getListaMalotes().add(pedidoMalote);
                    }
                }

            }

            fileReader.close();

            if (this.listaAgencias.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("ArquivoInvalido"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.mensagemImportacao = getMessageS("MensagemImportacaoPreOrder")
                        .replace("%s1", String.valueOf(this.volumesPreOrder))
                        .replace("%s2", String.valueOf(this.listaAgencias.size()));
                PrimeFaces.current().executeScript("PF('dlgPreOrders').show();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void realizarUploadPedido(FileUploadEvent event) {
        try {
            new File("C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\pedidos\\" + getDataAtual("SQL")).mkdirs();
            String arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\pedidos\\" + getDataAtual("SQL") + "\\" + event.getFile().getFileName();
            File file = new File(arquivo);
            if (file.exists()) {
                arquivo = "C:\\xampp\\htdocs\\satellite\\documentos\\" + this.persistencia.getEmpresa() + "\\pedidos\\" + getDataAtual("SQL") + "\\" + getDataAtual("HHMMSS") + event.getFile().getFileName();
                file = new File(arquivo);
            }

            FileOutputStream output = new FileOutputStream(file);
            output.write(event.getFile().getContents());
            output.close();
            this.arquivosPedidos.add(file);

            FileReader fileReader = new FileReader(file);
            BufferedReader bufferedReader = new BufferedReader(fileReader);
            String linha;
            String[] dados;
            this.pedidosImportados = new ArrayList<>();
            this.volumesPreOrder = 0;
            ImportacaoPedido importacao;
            Clientes cliente1, cliente2;
            PreOrderVol preOrderVol;
            while ((linha = bufferedReader.readLine()) != null) {
                this.logerro.Grava("leitura arquivo: " + linha, caminho);
                try {
                    dados = linha.split("\\|");
                    importacao = new ImportacaoPedido();
                    importacao.getPedido().setDtColeta(dados[0]);
                    importacao.getPedido().setHora1D(LocalTime.parse(dados[1]).minusMinutes(30).format(DateTimeFormatter.ofPattern("HH:mm")));
                    importacao.getPedido().setHora2D(LocalTime.parse(dados[1]).plusMinutes(30).format(DateTimeFormatter.ofPattern("HH:mm")));

                    cliente1 = this.guiasweb.buscarClientePedido(dados[2], this.codFil, this.persistencia);
                    importacao.getPedido().setCodCli1(cliente1.getCodigo());
                    importacao.getPedido().setNRed1(cliente1.getNRed());

                    cliente2 = this.guiasweb.buscarClientePedido(dados[3], this.codFil, this.persistencia);
                    importacao.getPedido().setCodCli2(cliente2.getCodigo());
                    importacao.getPedido().setNRed2(cliente2.getNRed());

                    importacao.getPedido().setValor(dados[4]);
                    importacao.getPedido().setObs(dados[5]);

                    importacao.getPedido().setHora1O("07:45");
                    importacao.getPedido().setHora2O("08:15");
                    importacao.getPedido().setSituacao("PD");
                    importacao.getPedido().setClassifSrv("V");
                    importacao.getPedido().setCodFil(this.codFil);
                    importacao.getPedido().setSolicitante(this.operador);
                    importacao.getPedido().setOperIncl(this.operador);
                    importacao.getPedido().setDt_Incl(getDataAtual("SQL"));
                    importacao.getPedido().setHr_Incl(getDataAtual("HORA"));
                    importacao.getPedido().setOperador(this.operador);
                    importacao.getPedido().setDt_Alter(getDataAtual("SQL"));
                    importacao.getPedido().setHr_Alter(getDataAtual("HORA"));
                    importacao.getPedido().setFlag_Excl("");

                    preOrderVol = new PreOrderVol();
                    preOrderVol.setLacre(dados[6]);
                    preOrderVol.setQtde(dados[7]);
                    preOrderVol.setCodFil(this.codFil);
                    preOrderVol.setValor(new BigDecimal(preOrderVol.getQtde()).multiply(new BigDecimal(preOrderVol.getLacre())).toPlainString());
                    if (new BigDecimal(preOrderVol.getQtde()).compareTo(BigDecimal.ZERO) != 0) {
                        importacao.getComposicoes().add(preOrderVol);
                    }

                    preOrderVol = new PreOrderVol();
                    preOrderVol.setLacre(dados[8]);
                    preOrderVol.setQtde(dados[9]);
                    preOrderVol.setCodFil(this.codFil);
                    preOrderVol.setValor(new BigDecimal(preOrderVol.getQtde()).multiply(new BigDecimal(preOrderVol.getLacre())).toPlainString());
                    if (new BigDecimal(preOrderVol.getQtde()).compareTo(BigDecimal.ZERO) != 0) {
                        importacao.getComposicoes().add(preOrderVol);
                    }

                    preOrderVol = new PreOrderVol();
                    preOrderVol.setLacre(dados[10]);
                    preOrderVol.setQtde(dados[11]);
                    preOrderVol.setCodFil(this.codFil);
                    preOrderVol.setValor(new BigDecimal(preOrderVol.getQtde()).multiply(new BigDecimal(preOrderVol.getLacre())).toPlainString());
                    if (new BigDecimal(preOrderVol.getQtde()).compareTo(BigDecimal.ZERO) != 0) {
                        importacao.getComposicoes().add(preOrderVol);
                    }

                    preOrderVol = new PreOrderVol();
                    preOrderVol.setLacre(dados[12]);
                    preOrderVol.setQtde(dados[13]);
                    preOrderVol.setCodFil(this.codFil);
                    preOrderVol.setValor(new BigDecimal(preOrderVol.getQtde()).multiply(new BigDecimal(preOrderVol.getLacre())).toPlainString());
                    if (new BigDecimal(preOrderVol.getQtde()).compareTo(BigDecimal.ZERO) != 0) {
                        importacao.getComposicoes().add(preOrderVol);
                    }

                    preOrderVol = new PreOrderVol();
                    preOrderVol.setLacre(dados[14]);
                    preOrderVol.setQtde(dados[15]);
                    preOrderVol.setCodFil(this.codFil);
                    preOrderVol.setValor(new BigDecimal(preOrderVol.getQtde()).multiply(new BigDecimal(preOrderVol.getLacre())).toPlainString());
                    if (new BigDecimal(preOrderVol.getQtde()).compareTo(BigDecimal.ZERO) != 0) {
                        importacao.getComposicoes().add(preOrderVol);
                    }

                    preOrderVol = new PreOrderVol();
                    preOrderVol.setLacre(dados[16]);
                    preOrderVol.setQtde(dados[17]);
                    preOrderVol.setCodFil(this.codFil);
                    preOrderVol.setValor(new BigDecimal(preOrderVol.getQtde()).multiply(new BigDecimal(preOrderVol.getLacre())).toPlainString());
                    if (new BigDecimal(preOrderVol.getQtde()).compareTo(BigDecimal.ZERO) != 0) {
                        importacao.getComposicoes().add(preOrderVol);
                    }

                    this.pedidosImportados.add(importacao);
                } catch (Exception e2) {
                    this.logerro.Grava("erro leitura: " + e2.getMessage(), caminho);
                }
            }

            fileReader.close();

            if (this.pedidosImportados.isEmpty()) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("ArquivoInvalido"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            } else {
                this.mensagemImportacao = getMessageS("MensagemImportacaoPedido")
                        .replace("%s1", String.valueOf(this.pedidosImportados.size()));
                PrimeFaces.current().ajax().update("formPedidosImportados");
                PrimeFaces.current().executeScript("PF('dlgPedidosImportados').show();");
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void inserirPedidos() {
        try {
            this.guiasweb.inserirPedidosImportados(this.pedidosImportados, this.persistencia);
            PrimeFaces.current().executeScript("PF('dlgPedidosImportados').hide();");
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("ImportacaoCompleta"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void onTabChange(TabChangeEvent event) {
        gerarGraficos();
    }

    public void gerarGraficos() {
        graficoAlivios();
        graficoReforcos();
        PrimeFaces.current().ajax().update("main:mainTabView:graficos");
    }

    public void graficoAlivios() {
        try {
            this.aliviosNaDataGrafico = new PieChartModel();
            ChartData dados = new ChartData();
            PieChartDataSet conjuntoDados = new PieChartDataSet();
            PieChartOptions opcoesDados = new PieChartOptions();
            this.aliviosNaData = this.guiasweb.graficoAlivios(this.data1, this.data2, this.codPessoa.toPlainString(), this.persistencia);

            List<Number> valores = new ArrayList<>();
            List<String> rotulos = new ArrayList<>();

            BigDecimal vt = BigDecimal.ZERO;

            for (Rt_Perc rt_Perc : this.aliviosNaData) {
                vt = vt.add(new BigDecimal(rt_Perc.getValor()));
            }
            MathContext d2 = new MathContext(2);
            BigDecimal normalizador = new BigDecimal("100").divide(vt, MathContext.DECIMAL128);
            for (Rt_Perc rt_Perc : this.aliviosNaData) {
                valores.add(new BigDecimal(rt_Perc.getValor()).multiply(normalizador, d2));
                rotulos.add(rt_Perc.getNRed() + " - " + Moeda(rt_Perc.getValor()));
            }

            conjuntoDados.setData(valores);
            conjuntoDados.setBackgroundColor(Cores.gerarCoresAlternadas(this.aliviosNaData.size()));

            dados.addChartDataSet(conjuntoDados);
            dados.setLabels(rotulos);

            Title title = new Title();
            title.setDisplay(true);
            title.setFontSize(20);
            title.setText("Alívios no intervalo " + Mascaras.Data(data1) + " - " + Mascaras.Data(data2));
            opcoesDados.setTitle(title);

            Legend legend = new Legend();
            legend.setDisplay(true);
            legend.setPosition("right");
            opcoesDados.setLegend(legend);

            this.aliviosNaDataGrafico.setOptions(opcoesDados);
            this.aliviosNaDataGrafico.setData(dados);
            this.aliviosNaDataGrafico.setExtender("chartExtender");
        } catch (Exception e) {
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void graficoReforcos() {
        try {
            this.reforcosNaDataGrafico = new PieChartModel();
            ChartData dados = new ChartData();
            PieChartDataSet conjuntoDados = new PieChartDataSet();
            PieChartOptions opcoesDados = new PieChartOptions();
            this.reforcosNaData = this.guiasweb.graficoReforcos(this.data1, this.data2, this.codPessoa.toPlainString(), this.persistencia);

            List<Number> valores = new ArrayList<>();
            List<String> rotulos = new ArrayList<>();

            BigDecimal vt = BigDecimal.ZERO;

            for (Rt_Perc rt_Perc : this.reforcosNaData) {
                vt = vt.add(new BigDecimal(rt_Perc.getValor()));
            }
            MathContext d2 = new MathContext(2);
            BigDecimal normalizador = new BigDecimal("100").divide(vt, MathContext.DECIMAL128);
            for (Rt_Perc rt_Perc : this.reforcosNaData) {
                valores.add(new BigDecimal(rt_Perc.getValor()).multiply(normalizador, d2));
                rotulos.add(rt_Perc.getNRed() + " - " + Moeda(rt_Perc.getValor()));
            }

            conjuntoDados.setData(valores);
            conjuntoDados.setBackgroundColor(Cores.gerarCoresAlternadas(this.reforcosNaData.size()));

            dados.addChartDataSet(conjuntoDados);
            dados.setLabels(rotulos);

            Title title = new Title();
            title.setDisplay(true);
            title.setFontSize(20);
            title.setText("Reforços no intervalo " + Mascaras.Data(data1) + " - " + Mascaras.Data(data2));
            opcoesDados.setTitle(title);

            LegendLabel legendLabel = new LegendLabel();
            if (this.reforcosNaData.size() > 20 && this.reforcosNaData.size() <= 40) {
                legendLabel.setBoxWidth(20);
            } else if (this.reforcosNaData.size() > 40) {
                legendLabel.setBoxWidth(10);
            }

            Legend legend = new Legend();
            legend.setDisplay(true);
            legend.setPosition("right");
            legend.setLabels(legendLabel);
            opcoesDados.setLegend(legend);

            this.reforcosNaDataGrafico.setData(dados);
            this.reforcosNaDataGrafico.setOptions(opcoesDados);
            this.reforcosNaDataGrafico.setExtender("chartExtender");
        } catch (Exception e) {
            this.log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(this.log, this.caminho);
        }
    }

    public void buttonActionComposicaoNovo(ActionEvent actionEvent) {
        this.editarComposicao = false;
        pedidoCedulaMoedaNovo = new PedidoDN();
        pedidoCedulaMoeda = new PedidoDN();
        pedidoCedulaMoedaNovo.setTipo("C");

        PrimeFaces.current().resetInputs("formCadastroComposicao:cadastrar");
        PrimeFaces.current().executeScript("PF('dlgCadastroComposicao').show();");
    }

    public void salvarDadosComposicao() throws Exception {
        if ((null == this.pedidoCedulaMoedaNovo.getQtde() || this.pedidoCedulaMoedaNovo.getQtde().equals(""))
                && (null == this.pedidoCedulaMoedaNovo.getValor() || this.pedidoCedulaMoedaNovo.getValor().equals(""))) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS("Obrigatorio"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            if (!this.editarComposicao) {
                // NOVO
                TratarDadosComposicao();
                this.listaPedidoCedulaMoeda.add(this.pedidoCedulaMoedaNovo);
            } else {
                // EDICAO
                for (int I = 0; I < listaPedidoCedulaMoeda.size(); I++) {
                    if (listaPedidoCedulaMoeda.get(I).getNumero().equals(this.pedidoCedulaMoeda.getNumero())) {
                        this.listaPedidoCedulaMoeda.get(I).setTipoDesc(this.pedidoCedulaMoedaNovo.getTipo().equals("C") ? getMessageS("Cedula") : getMessageS("Moeda"));
                        this.listaPedidoCedulaMoeda.get(I).setCodigo(this.pedidoCedulaMoedaNovo.getCodigo());
                        this.listaPedidoCedulaMoeda.get(I).setQtde(this.pedidoCedulaMoedaNovo.getQtde());
                        this.listaPedidoCedulaMoeda.get(I).setTipo(this.pedidoCedulaMoedaNovo.getTipo());

                        if (!this.pedidoCedulaMoedaNovo.getQtde().equals("")
                                && (null == this.pedidoCedulaMoedaNovo.getValor() || this.pedidoCedulaMoedaNovo.getValor().equals(""))) {
                            this.pedidoCedulaMoedaNovo.setValor(Double.toString(new Float(this.pedidoCedulaMoedaNovo.getQtde()) * new Float(this.pedidoCedulaMoedaNovo.getCodigo())));
                        }

                        this.listaPedidoCedulaMoeda.get(I).setValor(this.pedidoCedulaMoedaNovo.getValor());
                        break;
                    }
                }

                TratarDadosComposicao();
            }

            pedidoCedulaMoedaNovo = new PedidoDN();
            pedidoCedulaMoeda = new PedidoDN();
            pedidoCedulaMoedaNovo.setTipo("C");
            PrimeFaces.current().executeScript("PF('dlgCadastroComposicao').hide();");
        }
    }

    private void TratarDadosComposicao() {
        for (int I = 0; I < listaPedidoCedulaMoeda.size(); I++) {
            this.listaPedidoCedulaMoeda.get(I).setNumero(Integer.toString(I));
        }

        this.pedidoCedulaMoedaNovo.setNumero(Integer.toString(this.listaPedidoCedulaMoeda.size() + 1));
        this.pedidoCedulaMoedaNovo.setTipoDesc(this.pedidoCedulaMoedaNovo.getTipo().equals("C") ? getMessageS("Cedula") : getMessageS("Moeda"));
        if (null != this.pedidoCedulaMoedaNovo.getQtde() && !this.pedidoCedulaMoedaNovo.getQtde().equals("")
                && (null == this.pedidoCedulaMoedaNovo.getValor() || this.pedidoCedulaMoedaNovo.getValor().equals(""))) {
            this.pedidoCedulaMoedaNovo.setValor(Double.toString(new Float(this.pedidoCedulaMoedaNovo.getQtde()) * new Float(this.pedidoCedulaMoedaNovo.getCodigo())));
        }
    }

    public void onRowComposicaoSelect(SelectEvent event) {
        this.pedidoCedulaMoedaNovo = (PedidoDN) event.getObject();
    }

    public void dblSelectComposicao(SelectEvent event) {
        this.pedidoCedulaMoedaNovo = (PedidoDN) event.getObject();
        buttonActionComposicao(null);
    }

    public void excluirItemComposicao() {
        if (null == this.pedidoCedulaMoeda) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            for (int I = 0; I < listaPedidoCedulaMoeda.size(); I++) {
                if (listaPedidoCedulaMoeda.get(I).getNumero().equals(this.pedidoCedulaMoeda.getNumero())) {
                    listaPedidoCedulaMoeda.remove(I);
                }
            }
        }
    }

    public void buttonActionComposicao(ActionEvent actionEvent) {
        if (null == this.pedidoCedulaMoeda) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneItem"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.editarComposicao = true;
                if (null != this.pedidoCedulaMoedaNovo.getQtde()
                        && !this.pedidoCedulaMoedaNovo.getQtde().equals("")) {
                    this.pedidoCedulaMoedaNovo.setValor("");
                }
                PrimeFaces.current().resetInputs("formCadastroComposicao:cadastrar");
                PrimeFaces.current().executeScript("PF('dlgCadastroComposicao').show();");
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    /*
    
    
                PrimeFaces.current().executeScript("PF('dlgCadastroComposicao').hide();");
    
    
    
     */
    public void preparaEdicaoLacre() {
        if (null == this.preOrderSelecionadoDetalhado) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecionePedido"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            this.novoLacre = "";
            PrimeFaces.current().ajax().update("formEditarLacre");
            PrimeFaces.current().executeScript("PF('dlgEditarLacre').show();");
        }
    }

    public void editarLacre() {
        if (null == this.preOrderSelecionadoDetalhado) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO, getMessageS("SelecionePedido"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        } else {
            try {
                this.preOrderSelecionado.setOperador(FuncoesString.RecortaAteEspaço("WEB-" + this.operador, 0, 10));
                this.guiasweb.atualizarLacre(this.preOrderSelecionadoDetalhado, this.novoLacre, this.persistencia);
                PrimeFaces.current().ajax().update("main");
                PrimeFaces.current().executeScript("PF('dlgEditarLacre').hide();");
            } catch (Exception e) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public String getBanco() {
        return banco;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getCodFil() {
        return codFil == null ? null : codFil.replace(".0", "");
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public BigDecimal getCodPessoa() {
        return codPessoa;
    }

    public void setCodPessoa(BigDecimal codPessoa) {
        this.codPessoa = codPessoa;
    }

    public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public List<CxFGuiasVol> getLacres() {
        return lacres;
    }

    public void setLacres(List<CxFGuiasVol> lacres) {
        this.lacres = lacres;
    }

    public String getData1() {
        return data1;
    }

    public void setData1(String data1) {
        this.data1 = data1;
    }

    public String getData2() {
        return data2;
    }

    public void setData2(String data2) {
        this.data2 = data2;
    }

    public BigDecimal getValorGuias() {
        return valorGuias;
    }

    public void setValorGuias(BigDecimal valorGuias) {
        this.valorGuias = valorGuias;
    }

    public String getSenhaAtual() {
        return senhaAtual;
    }

    public void setSenhaAtual(String senhaAtual) {
        this.senhaAtual = senhaAtual;
    }

    public String getSenhaNova() {
        return senhaNova;
    }

    public void setSenhaNova(String senhaNova) {
        this.senhaNova = senhaNova;
    }

    public Clientes getOrigem() {
        return origem;
    }

    public void setOrigem(Clientes origem) {
        this.origem = origem;
    }

    public Clientes getDestino() {
        return destino;
    }

    public void setDestino(Clientes destino) {
        this.destino = destino;
    }

    public String getCodBarras() {
        return codBarras;
    }

    public void setCodBarras(String codBarras) {
        this.codBarras = codBarras;
    }

    public String getExtenso() {
        return extenso;
    }

    public void setExtenso(String extenso) {
        this.extenso = extenso;
    }

    public Clientes getFaturamento() {
        return faturamento;
    }

    public void setFaturamento(Clientes faturamento) {
        this.faturamento = faturamento;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public BigDecimal getVolumesGuias() {
        return volumesGuias;
    }

    public void setVolumesGuias(BigDecimal volumesGuias) {
        this.volumesGuias = volumesGuias;
    }

    public void gerarLog(String texto) {
        this.logerro.GravaMetodos(texto, this.caminho);
    }

    public String getAutenticacao() {
        return autenticacao;
    }

    public void setAutenticacao(String autenticacao) {
        this.autenticacao = autenticacao;
    }

    public String getVoltar() {
        return null == voltar ? "login.xhtml" : voltar;
    }

    public void setVoltar(String voltar) {
        this.voltar = voltar;
    }

    public EGtv getGuiaSelecionada() {
        return guiaSelecionada;
    }

    public void setGuiaSelecionada(EGtv guiaSelecionada) {
        this.guiaSelecionada = guiaSelecionada;
    }

    public List<OS_Vig> getListaOsVig() {
        return listaOsVig;
    }

    public void setListaOsVig(List<OS_Vig> listaOsVig) {
        this.listaOsVig = listaOsVig;
    }

    public OS_Vig getOs_vig() {
        return os_vig;
    }

    public void setOs_vig(OS_Vig os_vig) {
        this.os_vig = os_vig;
    }

    public String getTipoPedido() {
        return tipoPedido;
    }

    public void setTipoPedido(String tipoPedido) {
        this.tipoPedido = tipoPedido;
    }

    public OS_Vig getOs_vigSelecionado() {
        return os_vigSelecionado;
    }

    public void setOs_vigSelecionado(OS_Vig os_vigSelecionado) {
        this.os_vigSelecionado = os_vigSelecionado;
    }

    public String getOrigemteste() {
        return origemteste;
    }

    public void setOrigemteste(String origemteste) {
        this.origemteste = origemteste;
    }

    public Rt_Guias getGuia() {
        return guia;
    }

    public void setGuia(Rt_Guias guia) {
        this.guia = guia;
    }

    public String getNomecli() {
        return nomecli;
    }

    public void setNomecli(String nomecli) {
        this.nomecli = nomecli;
    }

    public List<PreOrder> getPedidosRecentes() {
        return pedidosRecentes;
    }

    public void setPedidosRecentes(List<PreOrder> pedidosRecentes) {
        this.pedidosRecentes = pedidosRecentes;
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor;
    }

    public Pedido getPedido() {
        return pedido;
    }

    public void setPedido(Pedido pedido) {
        this.pedido = pedido;
    }

    public LocalDate getDataHoje() {
        return dataHoje;
    }

    public void setDataHoje(LocalDate dataHoje) {
        this.dataHoje = dataHoje;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getDatad() {
        return datad;
    }

    public void setDatad(String datad) {
        this.datad = datad;
    }

    public String getHora1o() {
        return hora1o;
    }

    public void setHora1o(String hora1o) {
        this.hora1o = hora1o;
    }

    public String getHora2o() {
        return hora2o;
    }

    public void setHora2o(String hora2o) {
        this.hora2o = hora2o;
    }

    public String getHora1d() {
        return hora1d;
    }

    public void setHora1d(String hora1d) {
        this.hora1d = hora1d;
    }

    public String getHora2d() {
        return hora2d;
    }

    public void setHora2d(String hora2d) {
        this.hora2d = hora2d;
    }

    public List<BBPedidoAgencia> getListaAgencias() {
        return listaAgencias;
    }

    public void setListaAgencias(List<BBPedidoAgencia> listaAgencias) {
        this.listaAgencias = listaAgencias;
    }

    public String getMensagemImportacao() {
        return mensagemImportacao;
    }

    public void setMensagemImportacao(String mensagemImportacao) {
        this.mensagemImportacao = mensagemImportacao;
    }

    public EGtv getGuiaPesquisa() {
        return guiaPesquisa;
    }

    public void setGuiaPesquisa(EGtv guiaPesquisa) {
        this.guiaPesquisa = guiaPesquisa;
    }

    public boolean isLimpar() {
        return limpar;
    }

    public void setLimpar(boolean limpar) {
        this.limpar = limpar;
    }

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getSubagencia() {
        return subagencia;
    }

    public void setSubagencia(String subagencia) {
        this.subagencia = subagencia;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public StreamedContent getArquivoDownload() {
        return arquivoDownload;
    }

    public void setArquivoDownload(StreamedContent arquivoDownload) {
        this.arquivoDownload = arquivoDownload;
    }

    public PreOrder getPreOrderSelecionado() {
        return preOrderSelecionado;
    }

    public void setPreOrderSelecionado(PreOrder preOrderSelecionado) {
        this.preOrderSelecionado = preOrderSelecionado;
    }

    public PieChartModel getAliviosNaDataGrafico() {
        return aliviosNaDataGrafico;
    }

    public void setAliviosNaDataGrafico(PieChartModel aliviosNaDataGrafico) {
        this.aliviosNaDataGrafico = aliviosNaDataGrafico;
    }

    public PieChartModel getReforcosNaDataGrafico() {
        return reforcosNaDataGrafico;
    }

    public void setReforcosNaDataGrafico(PieChartModel reforcosNaDataGrafico) {
        this.reforcosNaDataGrafico = reforcosNaDataGrafico;
    }

    public String getNovoLacre() {
        return novoLacre;
    }

    public void setNovoLacre(String novoLacre) {
        this.novoLacre = novoLacre;
    }

    public List<File> getArquivosPedidos() {
        return arquivosPedidos;
    }

    public void setArquivosPedidos(List<File> arquivosPedidos) {
        this.arquivosPedidos = arquivosPedidos;
    }

    public List<PreOrder> getPreOrderDetalhado() {
        return preOrderDetalhado;
    }

    public void setPreOrderDetalhado(List<PreOrder> preOrderDetalhado) {
        this.preOrderDetalhado = preOrderDetalhado;
    }

    public PreOrder getPreOrderSelecionadoDetalhado() {
        return preOrderSelecionadoDetalhado;
    }

    public void setPreOrderSelecionadoDetalhado(PreOrder preOrderSelecionadoDetalhado) {
        this.preOrderSelecionadoDetalhado = preOrderSelecionadoDetalhado;
    }

    public Filiais getFiliais() {
        return filiais;
    }

    public void setFiliais(Filiais filiais) {
        this.filiais = filiais;
    }

    public String getSequencia() {
        return sequencia;
    }

    public void setSequencia(String sequencia) {
        this.sequencia = sequencia;
    }

    public List<ImportacaoPedido> getPedidosImportados() {
        return pedidosImportados;
    }

    public void setPedidosImportados(List<ImportacaoPedido> pedidosImportados) {
        this.pedidosImportados = pedidosImportados;
    }

    public PedidoDN getPedidoCedulaMoeda() {
        return pedidoCedulaMoeda;
    }

    public void setPedidoCedulaMoeda(PedidoDN pedidoCedulaMoeda) {
        this.pedidoCedulaMoeda = pedidoCedulaMoeda;
    }

    public List<PedidoDN> getListaPedidoCedulaMoeda() {
        return listaPedidoCedulaMoeda;
    }

    public void setListaPedidoCedulaMoeda(List<PedidoDN> listaPedidoCedulaMoeda) {
        this.listaPedidoCedulaMoeda = listaPedidoCedulaMoeda;
    }

    public PedidoDN getPedidoCedulaMoedaNovo() {
        return pedidoCedulaMoedaNovo;
    }

    public void setPedidoCedulaMoedaNovo(PedidoDN pedidoCedulaMoedaNovo) {
        this.pedidoCedulaMoedaNovo = pedidoCedulaMoedaNovo;
    }

    public LazyDataModel<ExtratoFaturamento> getExtratos() {
        return extratos;
    }

    public void setExtratos(LazyDataModel<ExtratoFaturamento> extratos) {
        this.extratos = extratos;
    }

    public List<Date> getDatasSelecionadas() {
        return datasSelecionadas;
    }

    public void setDatasSelecionadas(List<Date> datasSelecionadas) {
        this.datasSelecionadas = datasSelecionadas;
    }

    public Date getDataSelecionada1() {
        return dataSelecionada1;
    }

    public void setDataSelecionada1(Date dataSelecionada1) {
        this.dataSelecionada1 = dataSelecionada1;
    }

    public Date getDataSelecionada2() {
        return dataSelecionada2;
    }

    public void setDataSelecionada2(Date dataSelecionada2) {
        this.dataSelecionada2 = dataSelecionada2;
    }

    // Métodos para Notas Fiscais
    public LazyDataModel<NFiscalCliente> getNotasFiscais() {
        try {
            if (this.notasFiscais == null) {
                System.out.println("Notas fiscais nulas");
                // Verificar se os dados necessários estão disponíveis
                if (this.data1 == null || this.data1.length() < 6 || this.codPessoa == null || this.codFil == null) {
                    System.out.println("Dados insuficientes para carregar notas fiscais");
                    System.out.println("data1: " + this.data1 + ", codPessoa: " + this.codPessoa + ", codFil: " + this.codFil);
                    return null;
                }

                System.out.println("data1 original: " + this.data1);
                System.out.println("data2 original: " + this.data2);

                // data1 está no formato YYYYMMDD, extrair competência YYYYMM
                String competencia = this.data1.substring(0, 6);

                System.out.println("Competência extraída: " + competencia);

                System.out.println("Criando LazyDataModel para notas fiscais - Competência: " + competencia);
                System.out.println("Parâmetros: codPessoa=" + this.codPessoa + ", codFil=" + this.codFil);
                System.out.println("Persistencias: local=" + (this.persistencia != null) + ", central=" + (this.central != null));

                this.notasFiscais = new NFiscalClienteLazyList(
                    this.persistencia,
                    this.central,
                    this.codPessoa,
                    this.codFil,
                    competencia
                );
                System.out.println("LazyDataModel criado com sucesso");
            }
            return this.notasFiscais;
        } catch (Exception e) {
            System.out.println("Erro ao criar LazyDataModel para notas fiscais: " + e.getMessage());
            e.printStackTrace();
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                "Erro ao carregar notas fiscais: " + e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            return null;
        }
    }

    public void setNotasFiscais(LazyDataModel<NFiscalCliente> notasFiscais) {
        this.notasFiscais = notasFiscais;
    }

    public NFiscalCliente getNfiscalSelecionada() {
        return nfiscalSelecionada;
    }

    public void setNfiscalSelecionada(NFiscalCliente nfiscalSelecionada) {
        this.nfiscalSelecionada = nfiscalSelecionada;
    }

    public List<BoletoCliente> getBoletosNF() {
        return boletosNF;
    }

    public void setBoletosNF(List<BoletoCliente> boletosNF) {
        this.boletosNF = boletosNF;
    }

    /**
     * Seleciona uma nota fiscal e carrega seus boletos
     */
    public void selecionarNotaFiscal(NFiscalCliente nf) {
        this.nfiscalSelecionada = nf;
        carregarBoletosNF();
    }

    /**
     * Carrega os boletos relacionados à nota fiscal selecionada
     */
    public void carregarBoletosNF() {
        try {
            if (this.nfiscalSelecionada != null && this.data1 != null && this.data1.length() >= 6) {
                String competencia = this.data1.substring(0, 6); // YYYYMM
                this.boletosNF = this.nfiscalDao.listarBoletosCliente(
                    this.codPessoa.toString(),
                    this.codFil,
                    competencia,
                    this.nfiscalSelecionada.getNumero(),
                    this.nfiscalSelecionada.getPraca(),
                    this.persistencia,
                    this.central
                );

                if (this.boletosNF == null) {
                    this.boletosNF = new ArrayList<>();
                }
            } else {
                this.boletosNF = new ArrayList<>();
            }
        } catch (Exception e) {
            System.out.println("Erro ao carregar boletos: " + e.getMessage());
            e.printStackTrace();
            this.boletosNF = new ArrayList<>();
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                "Erro ao carregar boletos: " + e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    /**
     * Abre uma nova janela com o boleto
     */
    public void visualizarBoleto(BoletoCliente boleto) {
        try {
            if (boleto != null && boleto.getUrl() != null && !boleto.getUrl().trim().isEmpty()) {
                PrimeFaces.current().executeScript("window.open('" + boleto.getUrl() + "', '_blank');");
            } else {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN,
                    "Boleto indisponível", null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception e) {
            System.out.println("Erro ao visualizar boleto: " + e.getMessage());
            e.printStackTrace();
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                "Erro ao visualizar boleto: " + e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    /**
     * Abre uma nova janela com a nota fiscal
     */
    public void visualizarNotaFiscal(NFiscalCliente nf) {
        try {
            if (nf != null) {
                // TODO: Implementar lógica para abrir NF da prefeitura ou modelo padrão
                // Por enquanto, apenas mostra mensagem
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_INFO,
                    "Visualização da NF " + nf.getNumero() + " será implementada", null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
            }
        } catch (Exception e) {
            System.out.println("Erro ao visualizar NF: " + e.getMessage());
            e.printStackTrace();
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                "Erro ao visualizar NF: " + e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    /**
     * Recarrega as notas fiscais quando o período é alterado
     */
    public void recarregarNotasFiscais() {
        this.notasFiscais = null;
    }

    // Getters e Setters para filtros de NF
    public String getFiltroNumeroNF() {
        return filtroNumeroNF;
    }

    public void setFiltroNumeroNF(String filtroNumeroNF) {
        this.filtroNumeroNF = filtroNumeroNF;
    }

    public String getFiltroPracaNF() {
        return filtroPracaNF;
    }

    public void setFiltroPracaNF(String filtroPracaNF) {
        this.filtroPracaNF = filtroPracaNF;
    }

    /**
     * Aplica filtros no extrato
     */
    public void aplicarFiltrosExtrato() {
        try {
            // Recarregar o extrato com os novos filtros
            this.extratos = null;
            this.extratoSimples = null;
            this.extratoCompleto = null;
            this.extratosFiltrados = null;
            getExtrato();
        } catch (Exception e) {
            System.out.println("Erro ao aplicar filtros: " + e.getMessage());
            e.printStackTrace();
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                "Erro ao aplicar filtros: " + e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    /**
     * Limpa os filtros do extrato
     */
    public void limparFiltrosExtrato() {
        this.filtroNumeroNF = null;
        this.filtroPracaNF = null;
        this.extratos = null;
        this.extratoSimples = null;
        this.extratoCompleto = null;
        this.extratosFiltrados = null;
    }

    /**
     * Aplica filtros no novo extrato
     */
    public void aplicarFiltrosExtratoNovo() {
        try {
            System.out.println("=== aplicarFiltrosExtratoNovo() CHAMADO ===");
            System.out.println("Filtros: NF=" + this.filtroNumeroNF + ", Praca=" + this.filtroPracaNF +
                             ", Guia=" + this.filtroGuia + ", Serie=" + this.filtroSerie);

            // Se filtrar por NF específica, usar consulta específica
            if (this.filtroNumeroNF != null && !this.filtroNumeroNF.trim().isEmpty() &&
                this.filtroPracaNF != null && !this.filtroPracaNF.trim().isEmpty()) {

                System.out.println("Aplicando filtro específico por NF");
                FatTVGuiasDao dao = new FatTVGuiasDao();
                this.extratoCompleto = dao.listarExtratoCliente(
                    this.codPessoa.toString(),
                    this.codFil,
                    null, // dataInicio - desconsiderado
                    null, // dataFim - desconsiderado
                    this.filtroNumeroNF.trim(),
                    this.filtroPracaNF.trim(),
                    this.persistencia,
                    this.central
                );
            } else {
                // Recarregar dados do período se necessário
                if (this.extratoCompleto == null) {
                    this.getExtratosFiltrados(); // Isso vai carregar os dados
                }
            }

            // Aplicar filtros locais
            this.extratosFiltrados = aplicarFiltrosExtrato(this.extratoCompleto);

            System.out.println("Filtros aplicados. Resultado: " +
                             (this.extratosFiltrados != null ? this.extratosFiltrados.size() : "null") + " registros");

        } catch (Exception e) {
            System.out.println("Erro ao aplicar filtros do extrato: " + e.getMessage());
            e.printStackTrace();
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                "Erro ao aplicar filtros: " + e.getMessage(), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
        }
    }

    /**
     * Limpa filtros do novo extrato
     */
    public void limparFiltrosExtratoNovo() {
        System.out.println("=== limparFiltrosExtratoNovo() CHAMADO ===");
        this.filtroNumeroNF = null;
        this.filtroPracaNF = null;
        this.filtroGuia = null;
        this.filtroSerie = null;
        this.extratoCompleto = null;
        this.extratosFiltrados = null;
        System.out.println("Filtros do extrato limpos");
    }

    /**
     * Toggle para mostrar/ocultar filtros do extrato (tecla P)
     */
    public void toggleFiltrosExtrato() {
        this.mostrarFiltrosExtrato = !this.mostrarFiltrosExtrato;
        System.out.println("Toggle filtros extrato: " + this.mostrarFiltrosExtrato);
    }

    /**
     * Toggle para mostrar/ocultar filtros das notas fiscais (tecla P)
     * NÃO limpa cache - apenas alterna visibilidade do painel
     */
    public void toggleFiltrosNF() {
        this.mostrarFiltrosNF = !this.mostrarFiltrosNF;
        System.out.println("Toggle filtros NF: " + this.mostrarFiltrosNF + " (sem nova consulta)");
        // NÃO limpar this.notasFiscaisFiltradas aqui - mantém cache
    }

    /**
     * Obtém lista de notas fiscais filtradas
     */
    public List<NFiscalCliente> getNotasFiscaisFiltradas() {
        try {
            System.out.println("=== getNotasFiscaisFiltradas() CHAMADO ===");
            System.out.println("notasFiscaisFiltradas é null? " + (this.notasFiscaisFiltradas == null));

            // Se já temos dados em cache, retornar sem nova consulta (igual ao extrato)
            if (this.notasFiscaisFiltradas != null && !this.notasFiscaisFiltradas.isEmpty()) {
                System.out.println("Retornando notas fiscais existentes: " + this.notasFiscaisFiltradas.size() + " registros");
                return this.notasFiscaisFiltradas;
            }

            System.out.println("Verificando dados necessários...");
            System.out.println("data1: " + this.data1);
            System.out.println("codPessoa: " + this.codPessoa);
            System.out.println("codFil: " + this.codFil);

            if (this.data1 == null || this.data1.length() < 6 || this.codPessoa == null || this.codFil == null) {
                System.out.println("ERRO: Dados insuficientes para carregar notas fiscais");
                return new ArrayList<>();
            }

            String competencia = this.data1.substring(0, 6);
            System.out.println("Carregando notas fiscais...");
            System.out.println("Competência: " + competencia);
            System.out.println("Filtro Número: " + this.filtroNumeroNF_NF);
            System.out.println("Filtro Tipo Serviço: " + this.filtroTipoServicoNF);

            // Buscar diretamente no banco com filtros aplicados
            this.notasFiscaisFiltradas = this.nfiscalDao.listarNotasFiscaisClienteComFiltros(
                this.codPessoa.toString(),
                this.codFil,
                competencia,
                this.filtroNumeroNF_NF,
                this.filtroTipoServicoNF,
                this.persistencia,
                this.central
            );

            System.out.println("Notas fiscais carregadas: " + (this.notasFiscaisFiltradas != null ? this.notasFiscaisFiltradas.size() : "null") + " registros");
            return this.notasFiscaisFiltradas != null ? this.notasFiscaisFiltradas : new ArrayList<>();

        } catch (Exception e) {
            System.out.println("ERRO em getNotasFiscaisFiltradas(): " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * Aplica filtros nas notas fiscais (força nova consulta)
     */
    public void aplicarFiltrosNF() {
        System.out.println("=== aplicarFiltrosNF() CHAMADO ===");
        System.out.println("Filtro Número NF: " + this.filtroNumeroNF_NF);
        System.out.println("Filtro Tipo Serviço: " + this.filtroTipoServicoNF);

        // Limpar cache para forçar nova consulta com filtros
        this.notasFiscaisFiltradas = null;
        System.out.println("Filtros de NF aplicados - nova consulta será executada");
    }

    /**
     * Limpa filtros das notas fiscais
     */
    public void limparFiltrosNF() {
        System.out.println("=== limparFiltrosNF() CHAMADO ===");
        this.filtroNumeroNF_NF = null;
        this.filtroTipoServicoNF = null;
        this.notasFiscaisFiltradas = null;
        System.out.println("Filtros de NF limpos - nova consulta sem filtros será executada");
    }

    // Getters e Setters para os novos filtros
    public String getFiltroGuia() {
        return filtroGuia;
    }

    public void setFiltroGuia(String filtroGuia) {
        this.filtroGuia = filtroGuia;
    }

    public String getFiltroSerie() {
        return filtroSerie;
    }

    public void setFiltroSerie(String filtroSerie) {
        this.filtroSerie = filtroSerie;
    }

    public boolean isMostrarFiltrosExtrato() {
        return mostrarFiltrosExtrato;
    }

    public void setMostrarFiltrosExtrato(boolean mostrarFiltrosExtrato) {
        this.mostrarFiltrosExtrato = mostrarFiltrosExtrato;
    }

    // Getters e Setters para filtros de NF
    public String getFiltroNumeroNF_NF() {
        return filtroNumeroNF_NF;
    }

    public void setFiltroNumeroNF_NF(String filtroNumeroNF_NF) {
        this.filtroNumeroNF_NF = filtroNumeroNF_NF;
    }

    public String getFiltroTipoServicoNF() {
        return filtroTipoServicoNF;
    }

    public void setFiltroTipoServicoNF(String filtroTipoServicoNF) {
        this.filtroTipoServicoNF = filtroTipoServicoNF;
    }

    public boolean isMostrarFiltrosNF() {
        return mostrarFiltrosNF;
    }

    public void setMostrarFiltrosNF(boolean mostrarFiltrosNF) {
        this.mostrarFiltrosNF = mostrarFiltrosNF;
    }

    public void setNotasFiscaisFiltradas(List<NFiscalCliente> notasFiscaisFiltradas) {
        this.notasFiscaisFiltradas = notasFiscaisFiltradas;
    }

    /**
     * Limpa cache das notas fiscais quando período muda
     */
    public void limparCacheNotasFiscais() {
        this.notasFiscaisFiltradas = null;
        this.notasFiscais = null;
    }
}
